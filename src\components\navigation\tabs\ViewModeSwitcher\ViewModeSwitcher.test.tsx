import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ViewModeSwitcher } from './ViewModeSwitcher';
import { vi } from 'vitest';

// Mock the theme store
vi.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#2563eb',
      primaryForeground: '#ffffff',
      secondary: '#4f46e5',
      accent: '#7c3aed',
      warning: '#d97706',
      background: '#ffffff',
      surface: '#f9fafb',
      border: '#e5e7eb',
      text: '#111827',
      textSecondary: '#6b7280',
      textMuted: '#9ca3af',
      mutedForeground: '#64748b',
      hover: '#f1f5f9',
      shadow: 'rgba(0, 0, 0, 0.1)',
    },
  }),
}));

// Mock icons
const ListIcon = () => <div data-testid="list-icon">List</div>;
const GridIcon = () => <div data-testid="grid-icon">Grid</div>;
const KanbanIcon = () => <div data-testid="kanban-icon">Kanban</div>;

describe('ViewModeSwitcher', () => {
  const defaultProps = {
    viewModes: [
      { name: 'list', icon: <ListIcon /> },
      { name: 'grid', icon: <GridIcon /> },
      { name: 'kanban', icon: <KanbanIcon /> },
    ],
    activeViewMode: 'list',
    onViewModeChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders all view mode buttons', () => {
    render(<ViewModeSwitcher {...defaultProps} />);
    
    expect(screen.getByRole('button', { name: /switch to list view/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /switch to grid view/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /switch to kanban view/i })).toBeInTheDocument();
  });

  it('shows active view mode as pressed', () => {
    render(<ViewModeSwitcher {...defaultProps} />);
    
    const listButton = screen.getByRole('button', { name: /switch to list view/i });
    expect(listButton).toHaveAttribute('aria-pressed', 'true');
    expect(listButton).toBeDisabled();
  });

  it('shows inactive view modes as not pressed', () => {
    render(<ViewModeSwitcher {...defaultProps} />);
    
    const gridButton = screen.getByRole('button', { name: /switch to grid view/i });
    const kanbanButton = screen.getByRole('button', { name: /switch to kanban view/i });
    
    expect(gridButton).toHaveAttribute('aria-pressed', 'false');
    expect(kanbanButton).toHaveAttribute('aria-pressed', 'false');
    expect(gridButton).not.toBeDisabled();
    expect(kanbanButton).not.toBeDisabled();
  });

  it('calls onViewModeChange when inactive mode is clicked', async () => {
    const user = userEvent.setup();
    render(<ViewModeSwitcher {...defaultProps} />);

    const gridButton = screen.getByRole('button', { name: /switch to grid view/i });
    await user.click(gridButton);

    expect(defaultProps.onViewModeChange).toHaveBeenCalledWith('grid');
  });

  it('does not call onViewModeChange when active mode is clicked', async () => {
    const user = userEvent.setup();
    render(<ViewModeSwitcher {...defaultProps} />);

    const listButton = screen.getByRole('button', { name: /switch to list view/i });
    await user.click(listButton);

    expect(defaultProps.onViewModeChange).not.toHaveBeenCalled();
  });

  it('renders icons correctly', () => {
    render(<ViewModeSwitcher {...defaultProps} />);
    
    expect(screen.getByTestId('list-icon')).toBeInTheDocument();
    expect(screen.getByTestId('grid-icon')).toBeInTheDocument();
    expect(screen.getByTestId('kanban-icon')).toBeInTheDocument();
  });

  it('applies correct accessibility attributes', () => {
    render(<ViewModeSwitcher {...defaultProps} data-testid="view-switcher" />);

    expect(screen.getByTestId('view-switcher')).toBeInTheDocument();
    
    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toHaveAttribute('aria-label');
      expect(button).toHaveAttribute('aria-pressed');
      expect(button).toHaveAttribute('title');
    });
  });

  it('renders with custom className', () => {
    render(<ViewModeSwitcher {...defaultProps} className="custom-class" data-testid="view-switcher" />);
    
    const switcher = screen.getByTestId('view-switcher');
    expect(switcher).toHaveClass('custom-class');
  });
});
