import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { BrowserRouter } from 'react-router-dom';
import AppDynamicContent from './AppDynamicContent';
import { ListIcon, GridIcon, BarChartIcon, PlusIcon, UploadIcon } from '../../icons';

const meta: Meta<typeof AppDynamicContent> = {
  title: 'Layout/AppDynamicContent',
  component: AppDynamicContent,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A unified component that combines DynamicAppBottomBar with main content area for consistent UI patterns.',
      },
    },
  },
  decorators: [
    (Story: any) => (
      <BrowserRouter>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="px-4 sm:px-6 lg:px-8 py-4">
              <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Sample Application Header
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                This shows how AppDynamicContent works with a header
              </p>
            </div>
          </div>
          <Story />
        </div>
      </BrowserRouter>
    ),
  ],
  argTypes: {
    view: {
      description: 'Configuration object for the bottom bar and its functionality',
    },
    children: {
      description: 'Main content to display in the content area',
    },
    className: {
      description: 'Additional CSS classes for the container',
      control: 'text',
    },
    contentClassName: {
      description: 'Additional CSS classes for the content area',
      control: 'text',
    },
  },
};

export default meta;
type Story = StoryObj<typeof AppDynamicContent>;

// Sample content components
const SampleDashboard = () => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {[1, 2, 3].map(i => (
        <div key={i} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Metric {i}
          </h3>
          <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
            {(Math.random() * 1000).toFixed(0)}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            +{(Math.random() * 20).toFixed(1)}% from last month
          </p>
        </div>
      ))}
    </div>
    
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        Recent Activity
      </h3>
      <div className="space-y-3">
        {Array.from({ length: 5 }, (_, i) => (
          <div key={i} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">
              {i + 1}
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                Activity item {i + 1}
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {Math.floor(Math.random() * 60)} minutes ago
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

const SampleDataTable = () => (
  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
    <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
        Data Table
      </h3>
    </div>
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Name
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Date
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {Array.from({ length: 8 }, (_, i) => (
            <tr key={i}>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                Item {i + 1}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  i % 3 === 0 
                    ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                    : i % 3 === 1
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                    : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                }`}>
                  {i % 3 === 0 ? 'Active' : i % 3 === 1 ? 'Pending' : 'Inactive'}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">
                2024-01-{String(i + 1).padStart(2, '0')}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                  Edit
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  </div>
);

// Base view configuration
const baseView = {
  title: 'Dashboard',
  actions: [
    { label: 'New', onClick: () => console.log('New clicked'), isPrimary: true },
    { label: 'Export', onClick: () => console.log('Export clicked') },
  ],
  search: {
    filterTags: [{ id: 'active', label: 'Active Records', removable: true }],
    filterItems: [
      { id: 'active', label: 'Active', selected: true },
      { id: 'archived', label: 'Archived', selected: false },
      { id: 'draft', label: 'Draft', selected: false },
    ],
    onSearch: (query: string) => console.log('Search:', query),
    onTagRemove: (tagId: string) => console.log('Remove tag:', tagId),
    onFilterSelect: (filterId: string) => console.log('Filter selected:', filterId),
  },
  pagination: {
    currentRange: '1-20 of 100',
    onNext: () => console.log('Next page'),
    onPrev: () => console.log('Previous page'),
  },
  viewModes: [
    { name: 'List', icon: <ListIcon className="w-4 h-4" /> },
    { name: 'Grid', icon: <GridIcon className="w-4 h-4" /> },
    { name: 'Chart', icon: <BarChartIcon className="w-4 h-4" /> },
  ],
  activeViewMode: 'List',
};

export const Default: Story = {
  args: {
    view: baseView,
    children: <SampleDashboard />,
  },
};

export const WithDataTable: Story = {
  args: {
    view: {
      ...baseView,
      title: 'Data Management',
      actions: [
        { label: 'Add Record', onClick: () => console.log('Add clicked'), isPrimary: true },
        { label: 'Import', onClick: () => console.log('Import clicked') },
        { label: 'Export', onClick: () => console.log('Export clicked') },
      ],
    },
    children: <SampleDataTable />,
  },
};

export const MinimalContent: Story = {
  args: {
    view: {
      ...baseView,
      title: 'Simple View',
      actions: [
        { label: 'Create', onClick: () => console.log('Create clicked'), isPrimary: true },
      ],
    },
    children: (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          Welcome to Your App
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          This is a minimal content example showing how AppDynamicContent works.
        </p>
        <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
          Get Started
        </button>
      </div>
    ),
  },
};

export const CustomStyling: Story = {
  args: {
    view: baseView,
    className: 'bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900',
    contentClassName: 'py-12',
    children: (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          Custom Styled Content
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          This example shows how to apply custom styling to both the container and content area.
        </p>
      </div>
    ),
  },
};
