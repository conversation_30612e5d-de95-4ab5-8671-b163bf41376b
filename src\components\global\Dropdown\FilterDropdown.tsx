import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import {
  DropdownBase,
  DropdownTrigger,
  DropdownContent,
  DropdownItem,
  DropdownSection,
  DropdownSeparator,
} from './Dropdown';
import type { BaseDropdownProps } from './types';

// Filter-specific types
export interface FilterItem {
  id: string;
  label: string;
  selected?: boolean;
  disabled?: boolean;
  hasDropdown?: boolean;
}

export interface GroupByItem {
  id: string;
  label: string;
  hasDropdown?: boolean;
  disabled?: boolean;
}

export interface FavoriteItem {
  id: string;
  label: string;
  selected?: boolean;
  disabled?: boolean;
}

export interface FilterDropdownProps extends Omit<BaseDropdownProps, 'variant'> {
  trigger: React.ReactNode;
  filterItems?: FilterItem[];
  groupByItems?: GroupByItem[];
  favoriteItems?: FavoriteItem[];
  searchQuery?: string;
  onFilterSelect?: (filterId: string) => void;
  onGroupBySelect?: (groupId: string) => void;
  onFavoriteSelect?: (favoriteId: string) => void;
  onFavoriteDelete?: (favoriteId: string) => void;
  onAddCustomFilter?: () => void;
  onAddCustomGroup?: () => void;
  onSaveCurrentSearch?: () => void;
  compact?: boolean;
}

/**
 * Specialized filter dropdown built using the composition system
 * Provides three sections: Filters, Group By, and Favorites
 */
export const FilterDropdown: React.FC<FilterDropdownProps> = ({
  trigger,
  filterItems = [],
  groupByItems = [],
  favoriteItems = [],
  searchQuery = '',
  onFilterSelect,
  onGroupBySelect,
  onFavoriteSelect,
  onFavoriteDelete,
  onAddCustomFilter,
  onAddCustomGroup,
  onSaveCurrentSearch,
  compact = false,
  ...baseProps
}) => {
  const { colors } = useThemeStore();

  // Filter icons
  const FilterIcon = () => (
    <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
    </svg>
  );

  const GroupIcon = () => (
    <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
    </svg>
  );

  const StarIcon = () => (
    <svg className="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
    </svg>
  );

  const CheckIcon = () => (
    <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
    </svg>
  );

  const TrashIcon = () => (
    <svg className="w-full h-full" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
    </svg>
  );

  const renderFilterItems = () => {
    return filterItems.map((item) => (
      <DropdownItem
        key={item.id}
        icon={item.selected ? <CheckIcon /> : undefined}
        disabled={item.disabled}
        onSelect={() => onFilterSelect?.(item.id)}
      >
        {item.label}
      </DropdownItem>
    ));
  };

  const renderGroupByItems = () => {
    return groupByItems.map((item) => (
      <DropdownItem
        key={item.id}
        disabled={item.disabled}
        onSelect={() => onGroupBySelect?.(item.id)}
      >
        {item.label}
      </DropdownItem>
    ));
  };

  const renderFavoriteItems = () => {
    return favoriteItems.map((item) => (
      <div key={item.id} className="flex items-center justify-between group">
        <DropdownItem
          icon={item.selected ? <CheckIcon /> : undefined}
          disabled={item.disabled}
          onSelect={() => onFavoriteSelect?.(item.id)}
          className="flex-1 mr-2"
        >
          {item.label}
        </DropdownItem>
        {onFavoriteDelete && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onFavoriteDelete(item.id);
            }}
            className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-red-100 dark:hover:bg-red-900/20 transition-opacity"
            style={{ color: colors.error }}
            title="Delete favorite"
          >
            <TrashIcon />
          </button>
        )}
      </div>
    ));
  };

  return (
    <DropdownBase
      {...baseProps}
      variant="filter"
      size={compact ? 'small' : 'medium'}
      width={compact ? '600px' : '750px'}
      maxHeight={compact ? '250px' : '300px'}
      closeOnSelect={false}
    >
      <DropdownTrigger>{trigger}</DropdownTrigger>
      
      <DropdownContent className="grid grid-cols-3 gap-0">
        {/* Filters Section */}
        <div className="border-r" style={{ borderColor: colors.border }}>
          <DropdownSection
            title="Filters"
            icon={<FilterIcon />}
          >
            {renderFilterItems()}
            {onAddCustomFilter && (
              <>
                <DropdownSeparator />
                <DropdownItem onSelect={onAddCustomFilter}>
                  Add Custom Filter
                </DropdownItem>
              </>
            )}
          </DropdownSection>
        </div>

        {/* Group By Section */}
        <div className="border-r" style={{ borderColor: colors.border }}>
          <DropdownSection
            title="Group By"
            icon={<GroupIcon />}
          >
            {renderGroupByItems()}
            {onAddCustomGroup && (
              <>
                <DropdownSeparator />
                <DropdownItem onSelect={onAddCustomGroup}>
                  Add Custom Group
                </DropdownItem>
              </>
            )}
          </DropdownSection>
        </div>

        {/* Favorites Section */}
        <div>
          <DropdownSection
            title="Favorites"
            icon={<StarIcon />}
          >
            {renderFavoriteItems()}
            {onSaveCurrentSearch && searchQuery && (
              <>
                <DropdownSeparator />
                <DropdownItem onSelect={onSaveCurrentSearch}>
                  Save Current Search
                </DropdownItem>
              </>
            )}
          </DropdownSection>
        </div>
      </DropdownContent>
    </DropdownBase>
  );
};
