// Content Layout Components
// Main content areas, panels, and content organization

// Dynamic Content
export { default as AppDynamicContent } from './AppDynamicContent';
export type { AppDynamicContentProps } from './AppDynamicContent';

// TODO: Add DynamicAppBottomBar when available
// export { default as DynamicAppBottomBar } from './DynamicAppBottomBar';
// export type { DynamicAppBottomBarProps } from './DynamicAppBottomBar';

// Layout Utilities (re-exported from global for convenience)
export { Separator } from '../../global/Separator';
export type { SeparatorProps } from '../../global/Separator';

// TODO: Add more content components
// export { default as MainContent } from './MainContent';
// export { default as ContentArea } from './ContentArea';
// export { default as Panel } from './Panel';
// export { default as Section } from './Section';
