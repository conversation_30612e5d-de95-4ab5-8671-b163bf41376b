// UI Components - Basic building blocks for the application
// These components should be highly reusable and follow design system principles
//
// ⚠️  MIGRATION NOTICE:
// Many components have been moved to better locations:
// - Button, Input, Label, TextArea → components/forms/
// - Card, Text, Heading, Separator → components/global/
// - ViewModeSwitcher, ViewToggle → components/global/ViewModeSelector
// - Dropdown, FilterDropdown → components/global/Dropdown

// Buttons (DEPRECATED - use components/forms/Button)
export { default as Button } from './Button/Button';
export type { ButtonProps } from './Button/Button';

// Inputs (DEPRECATED - use components/forms/Input)
export { default as Input } from './Input/Input';
export type { InputProps } from './Input/Input';
export { default as PhoneInput } from './PhoneInput/PhoneInput';
export type { PhoneInputProps } from './PhoneInput/PhoneInput';

// Display (DEPRECATED - use components/global/Card)
export { default as Card } from './Card/Card';
export type { CardProps } from './Card/Card';

// Dashboard Components
export { default as NotificationBar } from './NotificationBar/NotificationBar';
export type { NotificationBarProps } from './NotificationBar/NotificationBar';

export { default as AppTile } from './AppTile/AppTile';
export type { AppTileProps } from './AppTile/AppTile';

export { default as SearchOverlay } from './SearchOverlay/SearchOverlay';
export type { SearchOverlayProps } from './SearchOverlay/SearchOverlay';

export { default as CustomerSupportModal } from './CustomerSupportModal/CustomerSupportModal';
export type { CustomerSupportModalProps } from './CustomerSupportModal/CustomerSupportModal';


export { default as CenteredSearchChipInput } from './CenteredSearchChipInput/CenteredSearchChipInput';
export type {
  CenteredSearchChipInputProps,
  ChipData,
  FilterOption,
} from './CenteredSearchChipInput/CenteredSearchChipInput';

export { default as TopNavigation } from './TopNavigation/TopNavigation';
export type { TopNavigationProps } from './TopNavigation/TopNavigation';

// Typography
export { default as Text } from './Text/Text';
export type { TextProps } from './Text/Text';
export { default as Heading } from './Heading/Heading';
export type { HeadingProps } from './Heading/Heading';
export { default as Label } from './Label/Label';
export type { LabelProps } from './Label/Label';
export { default as Caption } from './Caption/Caption';
export type { CaptionProps } from './Caption/Caption';

// Layout
export { default as Separator } from './Separator/Separator';
export type { SeparatorProps } from './Separator/Separator';

// Navigation
export { default as Pagination } from './Pagination/Pagination';
export type { PaginationProps } from './Pagination/Pagination';

export { default as ViewModeSwitcher } from './ViewModeSwitcher/ViewModeSwitcher';
export type { ViewModeSwitcherProps, ViewMode } from './ViewModeSwitcher/ViewModeSwitcher';

export { default as FilterDropdown } from './FilterDropdown/FilterDropdown';
export type { FilterDropdownProps } from './FilterDropdown/FilterDropdown';

// New UI Components
export { default as Dropdown } from './Dropdown/Dropdown';
export type { DropdownProps, DropdownItem } from './Dropdown/Dropdown';

export { default as CompanySelector } from './CompanySelector/CompanySelector';
export type { CompanySelectorProps } from './CompanySelector/CompanySelector';

export { default as UserAvatarDropdown } from './UserAvatarDropdown/UserAvatarDropdown';
export type { UserAvatarDropdownProps } from './UserAvatarDropdown/UserAvatarDropdown';

export { default as ThemeToggle } from './ThemeToggle/ThemeToggle';
export type { ThemeToggleProps } from './ThemeToggle/ThemeToggle';

// TODO: Add more components as they are implemented
// export { default as TextArea } from './TextArea/TextArea'
// export { default as Select } from './Select/Select'
// export { default as Checkbox } from './Checkbox/Checkbox'
// export { default as Radio } from './Radio/Radio'
// export { default as Badge } from './Badge/Badge'
// export { default as Avatar } from './Avatar/Avatar'
// export { default as Divider } from './Divider/Divider'
// export { default as Spinner } from './Spinner/Spinner'
// export { default as Skeleton } from './Skeleton/Skeleton'
// export { default as Icon } from './Icon/Icon'
