// UI Components - Basic building blocks for the application
// These components should be highly reusable and follow design system principles
//
// ⚠️  MIGRATION NOTICE:
// Many components have been moved to better locations:
// - Button, Input, Label, TextArea → components/forms/
// - Card, Text, Heading, Separator → components/global/
// - ViewModeSwitcher, ViewToggle → components/global/ViewModeSelector
// - Dropdown, FilterDropdown → components/global/Dropdown

// DEPRECATED COMPONENTS - Re-exported for backward compatibility
// Use the new locations for better tree-shaking and organization

// Buttons (DEPRECATED - use components/forms/Button)
export { Button } from '../forms/Button';
export type { ButtonProps } from '../forms/Button';

// Inputs (DEPRECATED - use components/forms/Input)
export { Input } from '../forms/Input';
export type { InputProps } from '../forms/Input';
export { default as PhoneInput } from '../inputs/basic/PhoneInput';
export type { PhoneInputProps } from '../inputs/basic/PhoneInput';

// Display (DEPRECATED - use components/global/Card)
export { Card } from '../global/Card';
export type { CardProps } from '../global/Card';

// Advanced Inputs (DEPRECATED - use components/inputs/advanced)
export { default as CenteredSearchChipInput } from '../inputs/advanced/CenteredSearchChipInput';
export type {
  CenteredSearchChipInputProps,
  ChipData,
  FilterOption,
} from '../inputs/advanced/CenteredSearchChipInput';

// Typography (DEPRECATED - use components/global)
export { Text } from '../global/Text';
export type { TextProps } from '../global/Text';
export { Heading } from '../global/Heading';
export type { HeadingProps } from '../global/Heading';
export { Label } from '../forms/Label';
export type { LabelProps } from '../forms/Label';

// Layout (DEPRECATED - use components/global)
export { Separator } from '../global/Separator';
export type { SeparatorProps } from '../global/Separator';

// Navigation (DEPRECATED - use components/global/ViewModeSelector)
export { ViewModeSelector as ViewModeSwitcher } from '../global/ViewModeSelector';
export type { ViewModeSelectorProps as ViewModeSwitcherProps } from '../global/ViewModeSelector';

// Dropdowns (DEPRECATED - use components/global/Dropdown)
export { FilterDropdown } from '../global/Dropdown';
export type { FilterDropdownProps } from '../global/Dropdown';

export { Dropdown } from '../global/Dropdown';
export type { DropdownProps, DropdownItemType as DropdownItem } from '../global/Dropdown';

// Selectors (DEPRECATED - use components/inputs/selectors)
export { default as CompanySelector } from '../inputs/selectors/CompanySelector';
export type { CompanySelectorProps } from '../inputs/selectors/CompanySelector';

export { CountrySelector } from '../forms/CountrySelector';
export type { CountrySelectorProps, Country } from '../forms/CountrySelector';

// Buttons (DEPRECATED - use components/inputs/buttons)
export { default as ThemeToggle } from '../inputs/buttons/ThemeToggle';
export type { ThemeToggleProps } from '../inputs/buttons/ThemeToggle';

// Form Components (DEPRECATED - use components/forms)
export { TextArea } from '../forms/TextArea';
export type { TextAreaProps } from '../forms/TextArea';

// TODO: Add more components as they are implemented
// export { default as Select } from './Select/Select'
// export { default as Checkbox } from './Checkbox/Checkbox'
// export { default as Radio } from './Radio/Radio'
// export { default as Badge } from './Badge/Badge'
// export { default as Avatar } from './Avatar/Avatar'
// export { default as Divider } from './Divider/Divider'
// export { default as Spinner } from './Spinner/Spinner'
// export { default as Skeleton } from './Skeleton/Skeleton'
// export { default as Icon } from './Icon/Icon'
