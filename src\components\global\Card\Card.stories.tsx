import type { Meta, StoryObj } from '@storybook/react';
import { Card } from './Card';
import { Text } from '../Text';
import { Heading } from '../Heading';
import { Button } from '../../forms/Button';

const meta: Meta<typeof Card> = {
  title: 'Global/Card',
  component: Card,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A flexible card component for displaying content with consistent styling across the application.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'elevated', 'outlined', 'filled'],
      description: 'Visual variant of the card',
    },
    padding: {
      control: { type: 'select' },
      options: ['none', 'sm', 'md', 'lg'],
      description: 'Internal padding of the card',
    },
    hoverable: {
      control: 'boolean',
      description: 'Whether the card has hover effects',
    },
    clickable: {
      control: 'boolean',
      description: 'Whether the card is clickable',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic card
export const Default: Story = {
  args: {
    children: (
      <div>
        <Heading level={3} className="mb-2">Card Title</Heading>
        <Text>This is a basic card with default styling.</Text>
      </div>
    ),
  },
};

// Elevated card
export const Elevated: Story = {
  args: {
    variant: 'elevated',
    children: (
      <div>
        <Heading level={3} className="mb-2">Elevated Card</Heading>
        <Text>This card has a shadow to appear elevated above the surface.</Text>
      </div>
    ),
  },
};

// Outlined card
export const Outlined: Story = {
  args: {
    variant: 'outlined',
    children: (
      <div>
        <Heading level={3} className="mb-2">Outlined Card</Heading>
        <Text>This card has a prominent border instead of a shadow.</Text>
      </div>
    ),
  },
};

// Filled card
export const Filled: Story = {
  args: {
    variant: 'filled',
    children: (
      <div>
        <Heading level={3} className="mb-2">Filled Card</Heading>
        <Text>This card has a filled background color.</Text>
      </div>
    ),
  },
};

// Hoverable card
export const Hoverable: Story = {
  args: {
    hoverable: true,
    children: (
      <div>
        <Heading level={3} className="mb-2">Hoverable Card</Heading>
        <Text>Hover over this card to see the effect.</Text>
      </div>
    ),
  },
};

// Clickable card
export const Clickable: Story = {
  args: {
    clickable: true,
    onClick: () => alert('Card clicked!'),
    children: (
      <div>
        <Heading level={3} className="mb-2">Clickable Card</Heading>
        <Text>Click anywhere on this card to trigger an action.</Text>
      </div>
    ),
  },
};

// Different padding sizes
export const PaddingSizes: Story = {
  render: () => (
    <div className="space-y-4">
      <Card padding="none">
        <Text>No padding</Text>
      </Card>
      <Card padding="sm">
        <Text>Small padding</Text>
      </Card>
      <Card padding="md">
        <Text>Medium padding (default)</Text>
      </Card>
      <Card padding="lg">
        <Text>Large padding</Text>
      </Card>
    </div>
  ),
};

// Complex content
export const ComplexContent: Story = {
  args: {
    variant: 'elevated',
    hoverable: true,
    children: (
      <div>
        <div className="flex items-center justify-between mb-4">
          <Heading level={3}>Product Card</Heading>
          <Text variant="caption" color="muted">$99.99</Text>
        </div>
        <Text className="mb-4">
          This is a more complex card with multiple elements including headings, 
          text, and interactive buttons.
        </Text>
        <div className="flex space-x-2">
          <Button variant="primary" size="sm">
            Add to Cart
          </Button>
          <Button variant="outline" size="sm">
            View Details
          </Button>
        </div>
      </div>
    ),
  },
};
