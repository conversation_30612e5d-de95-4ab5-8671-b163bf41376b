import React, { useState } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface NotificationBarProps {
  message: string;
  type?: 'info' | 'warning' | 'error' | 'success';
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
  'data-testid'?: string;
}

const NotificationBar: React.FC<NotificationBarProps> = ({
  message,
  type = 'info',
  dismissible = true,
  onDismiss,
  className = '',
  'data-testid': testId,
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const { colors } = useThemeStore();

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  if (!isVisible) return null;

  const typeStyles = {
    info: {
      backgroundColor: colors.info,
      color: colors.infoForeground,
      borderColor: colors.info,
    },
    warning: {
      backgroundColor: colors.warning,
      color: colors.warningForeground,
      borderColor: colors.warning,
    },
    error: {
      backgroundColor: colors.error,
      color: colors.errorForeground,
      borderColor: colors.error,
    },
    success: {
      backgroundColor: colors.success,
      color: colors.successForeground,
      borderColor: colors.success,
    },
  };

  const baseClasses = cn(
    'w-full px-4 py-3 flex items-center justify-between',
    'border-b transition-all duration-300 ease-in-out',
    'text-sm font-medium animate-slide-in-top',
    className
  );

  return (
    <div className={baseClasses} style={typeStyles[type]} data-testid={testId}>
      <div className="flex-1 text-center">{message}</div>

      {dismissible && (
        <button
          onClick={handleDismiss}
          className="ml-4 p-1 rounded-full hover:bg-black/10 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white/20"
          aria-label="Dismiss notification"
        >
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      )}
    </div>
  );
};

export default NotificationBar;
