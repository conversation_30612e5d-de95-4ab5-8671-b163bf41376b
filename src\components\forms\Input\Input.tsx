import React, { forwardRef } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import type { FormControlProps } from '../types';

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'onChange'>,
    FormControlProps {
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  clearable?: boolean;
  onClear?: () => void;
}

/**
 * Enhanced Input component for forms
 * Moved from ui/Input with form-specific enhancements
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      helperText,
      validationState = 'default',
      size = 'md',
      variant = 'default',
      fullWidth = false,
      startIcon,
      endIcon,
      clearable = false,
      onClear,
      value,
      onChange,
      className = '',
      disabled = false,
      required = false,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const { colors } = useThemeStore();

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-4 py-3 text-lg',
    };

    const baseInputClasses =
      'w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed';

    const getValidationColor = () => {
      if (error || validationState === 'error') return colors.error;
      if (validationState === 'warning') return colors.warning;
      if (validationState === 'success') return colors.success;
      return colors.border;
    };

    const getVariantStyles = () => {
      const borderColor = getValidationColor();
      
      switch (variant) {
        case 'filled':
          return {
            backgroundColor: colors.muted,
            borderColor: 'transparent',
            color: colors.text,
            '--focus-ring-color': colors.primary,
          };
        case 'outlined':
          return {
            backgroundColor: 'transparent',
            borderColor,
            color: colors.text,
            '--focus-ring-color': colors.primary,
          };
        default:
          return {
            backgroundColor: colors.input,
            borderColor,
            color: colors.inputForeground,
            '--focus-ring-color': colors.primary,
          };
      }
    };

    const variantStyles = getVariantStyles();

    const inputClasses = cn(
      baseInputClasses,
      sizeClasses[size],
      startIcon && 'pl-10',
      (endIcon || clearable) && 'pr-10',
      className
    );

    const labelClasses = cn(
      'block text-sm font-medium mb-2',
      required && "after:content-['*'] after:ml-0.5 after:text-red-500"
    );

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange?.(e.target.value);
    };

    const handleClear = () => {
      onClear?.();
      onChange?.('');
    };

    const showClearButton = clearable && value && !disabled;

    return (
      <div className={fullWidth ? 'w-full' : ''}>
        {label && (
          <label
            className={labelClasses}
            style={{ color: error ? colors.error : colors.text }}
          >
            {label}
          </label>
        )}

        <div className="relative">
          {startIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div style={{ color: colors.mutedForeground }}>{startIcon}</div>
            </div>
          )}

          <input
            ref={ref}
            className={inputClasses}
            style={variantStyles}
            disabled={disabled}
            required={required}
            value={value}
            onChange={handleChange}
            data-testid={testId}
            {...props}
          />

          {(endIcon || showClearButton) && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {showClearButton ? (
                <button
                  type="button"
                  onClick={handleClear}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  tabIndex={-1}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              ) : (
                endIcon && (
                  <div style={{ color: colors.mutedForeground }}>{endIcon}</div>
                )
              )}
            </div>
          )}
        </div>

        {(error || helperText) && (
          <div className="mt-1">
            {error && (
              <p className="text-sm" style={{ color: colors.error }}>
                {error}
              </p>
            )}
            {helperText && !error && (
              <p className="text-sm" style={{ color: colors.mutedForeground }}>
                {helperText}
              </p>
            )}
          </div>
        )}
      </div>
    );
  }
);
