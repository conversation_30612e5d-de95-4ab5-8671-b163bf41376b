// Form Components - Components for building forms and handling user input
// These components provide form functionality with validation and accessibility

// Core form components (migrated from ui/)
export { Button } from './Button';
export type { ButtonProps } from './Button';

export { Input } from './Input';
export type { InputProps } from './Input';

export { Label } from './Label';
export type { LabelProps } from './Label';

export { TextArea } from './TextArea';
export type { TextAreaProps } from './TextArea';

// Specialized form components
export { CountrySelector } from './CountrySelector';
export type { CountrySelectorProps, Country } from './CountrySelector';

// Form types
export type {
  FormSize,
  FormVariant,
  FormValidationState,
  BaseFormProps,
  FormFieldProps,
  FormControlProps,
  FormValidationRule,
  FormFieldState,
  FormState,
  FormConfig,
  SelectOption,
  SelectGroup,
  FileUploadConfig,
  UploadedFile,
} from './types';
