import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ViewModeSelector } from '../ViewModeSelector';
import type { ViewModeOption } from '../types';

// Mock the theme store
jest.mock('../../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#3b82f6',
      primaryForeground: '#ffffff',
      text: '#1f2937',
      mutedForeground: '#6b7280',
      surface: '#ffffff',
      border: '#e5e7eb',
      hover: '#f3f4f6',
      shadow: '#000000',
    },
  }),
}));

const mockOptions: ViewModeOption[] = [
  {
    id: 'list',
    name: 'List',
    icon: '📋',
    description: 'List view',
  },
  {
    id: 'grid',
    name: 'Grid',
    icon: '⚏',
    description: 'Grid view',
  },
  {
    id: 'kanban',
    name: '<PERSON><PERSON><PERSON>',
    icon: '📊',
    description: 'Kanban view',
    disabled: true,
  },
];

describe('ViewModeSelector', () => {
  const defaultProps = {
    options: mockOptions,
    value: 'list',
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all options', () => {
    render(<ViewModeSelector {...defaultProps} />);

    expect(screen.getByText('List')).toBeInTheDocument();
    expect(screen.getByText('Grid')).toBeInTheDocument();
    expect(screen.getByText('Kanban')).toBeInTheDocument();
  });

  it('shows active state for selected option', () => {
    render(<ViewModeSelector {...defaultProps} value="grid" />);

    const gridButton = screen.getByRole('radio', { name: /grid/i });
    expect(gridButton).toHaveAttribute('aria-checked', 'true');

    const listButton = screen.getByRole('radio', { name: /list/i });
    expect(listButton).toHaveAttribute('aria-checked', 'false');
  });

  it('calls onChange when option is clicked', () => {
    const onChange = jest.fn();
    render(<ViewModeSelector {...defaultProps} onChange={onChange} />);

    const gridButton = screen.getByRole('radio', { name: /grid/i });
    fireEvent.click(gridButton);

    expect(onChange).toHaveBeenCalledWith('grid');
  });

  it('does not call onChange for disabled options', () => {
    const onChange = jest.fn();
    render(<ViewModeSelector {...defaultProps} onChange={onChange} />);

    const kanbanButton = screen.getByRole('radio', { name: /kanban/i });
    fireEvent.click(kanbanButton);

    expect(onChange).not.toHaveBeenCalled();
  });

  it('does not call onChange when clicking the already selected option', () => {
    const onChange = jest.fn();
    render(<ViewModeSelector {...defaultProps} onChange={onChange} />);

    const listButton = screen.getByRole('radio', { name: /list/i });
    fireEvent.click(listButton);

    expect(onChange).not.toHaveBeenCalled();
  });

  it('allows deselection when allowDeselect is true', () => {
    const onChange = jest.fn();
    render(
      <ViewModeSelector 
        {...defaultProps} 
        onChange={onChange} 
        allowDeselect 
      />
    );

    const listButton = screen.getByRole('radio', { name: /list/i });
    fireEvent.click(listButton);

    expect(onChange).toHaveBeenCalledWith('');
  });

  it('renders with toggle variant', () => {
    render(
      <ViewModeSelector 
        {...defaultProps} 
        variant="toggle" 
        showLabels 
      />
    );

    // Should render as radiogroup
    expect(screen.getByRole('radiogroup')).toBeInTheDocument();
    
    // Should show labels
    expect(screen.getByText('List')).toBeInTheDocument();
    expect(screen.getByText('Grid')).toBeInTheDocument();
  });

  it('renders with compact variant without labels', () => {
    render(
      <ViewModeSelector 
        {...defaultProps} 
        variant="compact" 
        showLabels={false}
      />
    );

    // Should not show text labels, only icons
    expect(screen.queryByText('List')).not.toBeInTheDocument();
    expect(screen.queryByText('Grid')).not.toBeInTheDocument();
  });

  it('renders with selector variant showing descriptions', () => {
    render(
      <ViewModeSelector 
        {...defaultProps} 
        variant="selector" 
        showDescriptions 
      />
    );

    expect(screen.getByText('List view')).toBeInTheDocument();
    expect(screen.getByText('Grid view')).toBeInTheDocument();
    expect(screen.getByText('Kanban view')).toBeInTheDocument();
  });

  it('handles keyboard navigation', () => {
    const onChange = jest.fn();
    render(<ViewModeSelector {...defaultProps} onChange={onChange} />);

    const gridButton = screen.getByRole('radio', { name: /grid/i });
    
    // Test Enter key
    fireEvent.keyDown(gridButton, { key: 'Enter' });
    expect(onChange).toHaveBeenCalledWith('grid');

    onChange.mockClear();

    // Test Space key
    fireEvent.keyDown(gridButton, { key: ' ' });
    expect(onChange).toHaveBeenCalledWith('grid');
  });

  it('applies correct size classes', () => {
    const { rerender } = render(
      <ViewModeSelector {...defaultProps} size="small" />
    );

    let container = screen.getByRole('radiogroup');
    expect(container).toHaveClass('p-0.5');

    rerender(
      <ViewModeSelector {...defaultProps} size="large" />
    );

    container = screen.getByRole('radiogroup');
    expect(container).toHaveClass('p-2');
  });

  it('renders vertically when orientation is vertical', () => {
    render(
      <ViewModeSelector 
        {...defaultProps} 
        orientation="vertical" 
        variant="toggle"
      />
    );

    const container = screen.getByRole('radiogroup');
    expect(container).toHaveClass('flex-col');
  });

  it('sets correct accessibility attributes', () => {
    render(
      <ViewModeSelector 
        {...defaultProps} 
        aria-label="Choose view mode"
        data-testid="view-selector"
      />
    );

    const container = screen.getByRole('radiogroup');
    expect(container).toHaveAttribute('aria-label', 'Choose view mode');
    expect(container).toHaveAttribute('data-testid', 'view-selector');
  });
});
