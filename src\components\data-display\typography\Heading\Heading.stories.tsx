import type { Meta, StoryObj } from '@storybook/react-vite';
import Heading from './Heading';

const meta: Meta<typeof Heading> = {
  title: 'UI/Typography/Heading',
  component: Heading,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A semantic heading component with automatic sizing and styling based on heading level. Provides consistent typography hierarchy across the application.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    level: {
      control: { type: 'select' },
      options: [1, 2, 3, 4, 5, 6],
      description: 'Heading level (h1-h6)',
    },
    size: {
      control: { type: 'select' },
      options: [
        'xs',
        'sm',
        'base',
        'lg',
        'xl',
        '2xl',
        '3xl',
        '4xl',
        '5xl',
        '6xl',
      ],
      description: 'Size override for the heading',
    },
    weight: {
      control: { type: 'select' },
      options: ['normal', 'medium', 'semibold', 'bold', 'extrabold'],
      description: 'Font weight override',
    },
    color: {
      control: { type: 'select' },
      options: [
        'primary',
        'secondary',
        'muted',
        'error',
        'warning',
        'success',
        'info',
        'inherit',
      ],
      description: 'Text color variant',
    },
    align: {
      control: { type: 'select' },
      options: ['left', 'center', 'right'],
      description: 'Text alignment',
    },
    transform: {
      control: { type: 'select' },
      options: ['none', 'uppercase', 'lowercase', 'capitalize'],
      description: 'Text transformation',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Default Heading',
    level: 2,
  },
};

export const Levels: Story = {
  render: () => (
    <div className="space-y-4">
      <Heading level={1}>Heading 1 - Main page title</Heading>
      <Heading level={2}>Heading 2 - Section title</Heading>
      <Heading level={3}>Heading 3 - Subsection title</Heading>
      <Heading level={4}>Heading 4 - Component title</Heading>
      <Heading level={5}>Heading 5 - Small section</Heading>
      <Heading level={6}>Heading 6 - Smallest heading</Heading>
    </div>
  ),
};

export const Colors: Story = {
  render: () => (
    <div className="space-y-3">
      <Heading level={3} color="inherit">
        Inherit color (default)
      </Heading>
      <Heading level={3} color="primary">
        Primary color heading
      </Heading>
      <Heading level={3} color="secondary">
        Secondary color heading
      </Heading>
      <Heading level={3} color="muted">
        Muted color heading
      </Heading>
      <Heading level={3} color="error">
        Error color heading
      </Heading>
      <Heading level={3} color="warning">
        Warning color heading
      </Heading>
      <Heading level={3} color="success">
        Success color heading
      </Heading>
      <Heading level={3} color="info">
        Info color heading
      </Heading>
    </div>
  ),
};

export const CustomSizes: Story = {
  render: () => (
    <div className="space-y-3">
      <Heading level={3} size="sm">
        Small H3
      </Heading>
      <Heading level={3} size="base">
        Base H3
      </Heading>
      <Heading level={3} size="lg">
        Large H3
      </Heading>
      <Heading level={3} size="2xl">
        2XL H3
      </Heading>
      <Heading level={3} size="4xl">
        4XL H3
      </Heading>
    </div>
  ),
};

export const Weights: Story = {
  render: () => (
    <div className="space-y-3">
      <Heading level={3} weight="normal">
        Normal weight
      </Heading>
      <Heading level={3} weight="medium">
        Medium weight
      </Heading>
      <Heading level={3} weight="semibold">
        Semibold weight
      </Heading>
      <Heading level={3} weight="bold">
        Bold weight
      </Heading>
      <Heading level={3} weight="extrabold">
        Extrabold weight
      </Heading>
    </div>
  ),
};

export const Alignment: Story = {
  render: () => (
    <div className="w-full space-y-3">
      <Heading level={3} align="left">
        Left aligned heading
      </Heading>
      <Heading level={3} align="center">
        Center aligned heading
      </Heading>
      <Heading level={3} align="right">
        Right aligned heading
      </Heading>
    </div>
  ),
};

export const Truncation: Story = {
  render: () => (
    <div className="w-64 space-y-4">
      <div>
        <Heading level={4} truncate>
          This is a very long heading that will be truncated with ellipsis
        </Heading>
      </div>
      <div>
        <Heading level={4} lineClamp={2}>
          This is a longer heading that will be clamped to exactly two lines and
          show ellipsis
        </Heading>
      </div>
    </div>
  ),
};
