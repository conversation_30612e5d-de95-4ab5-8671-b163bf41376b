# Component Architecture Guide

This document outlines the component architecture and development patterns for the Nexed Web application.

## 🏗️ Folder Structure (Updated)

```
src/components/
├── global/             # Highly reusable components used across modules
├── ui/                 # Basic UI components (some deprecated)
├── layout/             # Layout components (Header, Sidebar, Container, etc.)
├── forms/              # Form-related components (Button, Input, Label, etc.)
├── navigation/         # Navigation components (Menu, Breadcrumb, etc.)
├── feedback/           # Feedback components (Toast, Modal, Alert, etc.)
├── data-display/       # Data display components (Table, List, etc.)
├── charts/             # Chart and visualization components
├── common/             # Common/shared utility components
└── index.ts           # Main export file
```

## 🚀 Recent Reorganization

The component architecture has been significantly improved following industry standards:

### ✅ What's New

1. **Global Components** (`src/components/global/`)
   - ViewModeSelector (unified view switching)
   - Enhanced Dropdown system with composition
   - Typography components (Text, Heading)
   - Layout components (Card, Separator)

2. **Enhanced Form Components** (`src/components/forms/`)
   - Better validation and accessibility
   - Consistent API across form controls

3. **Module-Based Architecture** (`src/modules/`)
   - User management moved to dedicated module
   - Better separation of concerns

### ⚠️ Migration Required

See [MIGRATION_GUIDE.md](./MIGRATION_GUIDE.md) for detailed migration instructions.

## Component Development Patterns

### 1. Component Structure

Each component should follow this structure:

```
ComponentName/
├── ComponentName.tsx      # Main component file
├── ComponentName.test.tsx # Unit tests
├── ComponentName.stories.tsx # Storybook stories
├── ComponentName.module.css # Component-specific styles (if needed)
├── index.ts              # Export file
└── types.ts              # Type definitions (if complex)
```

### 2. Component Template

```tsx
import React from 'react';
import { useThemeStore } from '../../stores/themeStore';
import { useAppStore } from '../../stores/appStore';

export interface ComponentNameProps {
  children?: React.ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  // Add other props as needed
}

const ComponentName: React.FC<ComponentNameProps> = ({
  children,
  className = '',
  variant = 'primary',
  size = 'md',
  disabled = false,
  ...props
}) => {
  const { colors } = useThemeStore();
  const { isFeatureEnabled } = useAppStore();

  // Component logic here

  return (
    <div
      className={`component-name ${variant} ${size} ${className}`}
      style={{
        '--primary-color': colors.primary,
        '--secondary-color': colors.secondary,
      }}
      {...props}
    >
      {children}
    </div>
  );
};

export default ComponentName;
```

### 3. Naming Conventions

- **Components**: PascalCase (e.g., `Button`, `FormField`, `DataTable`)
- **Props**: camelCase with descriptive names
- **Files**: Match component name exactly
- **CSS Classes**: kebab-case with component prefix

### 4. Design System Integration

- Use theme colors from `useThemeStore`
- Follow consistent spacing and typography
- Implement responsive design patterns
- Ensure accessibility compliance (WCAG 2.1 AA)

### 5. State Management

- Use Zustand stores for global state
- Keep component state local when possible
- Use feature flags from `useAppStore` for conditional features

### 6. Testing Requirements

- Unit tests for all components
- Accessibility tests
- Visual regression tests via Storybook
- Integration tests for complex components

### 7. Storybook Stories

Each component should have comprehensive Storybook stories:

```tsx
import type { Meta, StoryObj } from '@storybook/react';
import ComponentName from './ComponentName';

const meta: Meta<typeof ComponentName> = {
  title: 'UI/ComponentName',
  component: ComponentName,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'outline'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Component content',
  },
};

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary variant',
  },
};
```

## Best Practices

1. **Reusability**: Design components to be reusable across different contexts
2. **Composition**: Prefer composition over inheritance
3. **Props Interface**: Keep props interface simple and intuitive
4. **Performance**: Use React.memo for expensive components
5. **Accessibility**: Always include proper ARIA attributes
6. **Documentation**: Document complex props and usage patterns
7. **Error Handling**: Implement proper error boundaries
8. **TypeScript**: Use strict typing for all props and state

## Migration Guide

When migrating existing components:

1. Move component to appropriate category folder
2. Update imports throughout the application
3. Add proper TypeScript interfaces
4. Create Storybook stories
5. Add comprehensive tests
6. Update documentation

## Component Categories

### UI Components

Basic building blocks that are highly reusable and follow design system principles.

### Layout Components

Structural components that handle page layout and content organization.

### Form Components

Components specifically designed for form handling with validation and accessibility.

### Navigation Components

Components for user navigation and routing throughout the application.

### Feedback Components

Components that provide user feedback, notifications, and interactive overlays.

### Data Display Components

Components for displaying and organizing structured data.

### Chart Components

Specialized components for data visualization and analytics.

### Common Components

Utility components that provide shared functionality across the application.
