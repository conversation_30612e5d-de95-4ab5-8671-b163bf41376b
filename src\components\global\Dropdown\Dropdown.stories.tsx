import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { Dropdown, FilterDropdown } from './Dropdown';
import { useThemeStore } from '../../../stores/themeStore';

const meta: Meta<typeof Dropdown> = {
  title: 'Global/Dropdown',
  component: Dropdown,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Enhanced dropdown system with composition pattern. Supports simple items, sections, and specialized variants like FilterDropdown.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['default', 'menu', 'filter', 'select'],
      description: 'Visual variant of the dropdown',
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
      description: 'Size of the dropdown',
    },
    align: {
      control: { type: 'select' },
      options: ['left', 'right', 'center'],
      description: 'Alignment of the dropdown',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data
const sampleItems = [
  {
    id: '1',
    label: 'Edit',
    icon: '✏️',
    shortcut: '⌘E',
    onClick: () => console.log('Edit clicked'),
  },
  {
    id: '2',
    label: 'Duplicate',
    icon: '📋',
    shortcut: '⌘D',
    onClick: () => console.log('Duplicate clicked'),
  },
  {
    id: '3',
    label: 'Archive',
    icon: '📦',
    description: 'Move to archive',
    onClick: () => console.log('Archive clicked'),
  },
  {
    id: 'divider',
    label: '',
    isDivider: true,
  },
  {
    id: '4',
    label: 'Delete',
    icon: '🗑️',
    shortcut: '⌘⌫',
    onClick: () => console.log('Delete clicked'),
  },
];

const sampleSections = [
  {
    id: 'actions',
    title: 'Actions',
    icon: '⚡',
    items: [
      {
        id: '1',
        label: 'Edit',
        icon: '✏️',
        onClick: () => console.log('Edit clicked'),
      },
      {
        id: '2',
        label: 'Duplicate',
        icon: '📋',
        onClick: () => console.log('Duplicate clicked'),
      },
    ],
  },
  {
    id: 'organize',
    title: 'Organize',
    icon: '📁',
    items: [
      {
        id: '3',
        label: 'Move to folder',
        icon: '📂',
        onClick: () => console.log('Move clicked'),
      },
      {
        id: '4',
        label: 'Add to collection',
        icon: '📚',
        onClick: () => console.log('Add to collection clicked'),
      },
    ],
  },
  {
    id: 'danger',
    title: 'Danger Zone',
    icon: '⚠️',
    collapsible: true,
    defaultCollapsed: true,
    items: [
      {
        id: '5',
        label: 'Delete',
        icon: '🗑️',
        onClick: () => console.log('Delete clicked'),
      },
    ],
  },
];

// Template for interactive stories
const Template = (args: any) => {
  const { colors } = useThemeStore();

  return (
    <div className="p-8" style={{ backgroundColor: colors.background }}>
      <Dropdown
        {...args}
        trigger={
          <button
            className="px-4 py-2 rounded-lg border"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
              color: colors.text,
            }}
          >
            Open Dropdown ▼
          </button>
        }
      />
    </div>
  );
};

// Filter dropdown template
const FilterTemplate = (args: any) => {
  const { colors } = useThemeStore();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="p-8" style={{ backgroundColor: colors.background }}>
      <FilterDropdown
        {...args}
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        trigger={
          <button
            className="px-4 py-2 rounded-lg border"
            style={{
              backgroundColor: colors.surface,
              borderColor: colors.border,
              color: colors.text,
            }}
          >
            Filter Options ▼
          </button>
        }
      />
    </div>
  );
};

// Stories
export const Default: Story = {
  render: Template,
  args: {
    items: sampleItems,
    variant: 'default',
    size: 'medium',
    align: 'right',
  },
};

export const WithSections: Story = {
  render: Template,
  args: {
    sections: sampleSections,
    variant: 'menu',
    size: 'medium',
    align: 'right',
  },
};

export const Small: Story = {
  render: Template,
  args: {
    items: sampleItems.slice(0, 3),
    variant: 'default',
    size: 'small',
    align: 'right',
  },
};

export const Large: Story = {
  render: Template,
  args: {
    items: sampleItems,
    variant: 'menu',
    size: 'large',
    align: 'right',
  },
};

export const FilterDropdownStory: Story = {
  render: FilterTemplate,
  args: {
    filterItems: [
      { id: '1', label: 'Active', selected: true },
      { id: '2', label: 'Inactive', selected: false },
      { id: '3', label: 'Pending', selected: false },
    ],
    groupByItems: [
      { id: '1', label: 'Status' },
      { id: '2', label: 'Date Created' },
      { id: '3', label: 'Priority' },
    ],
    favoriteItems: [
      { id: '1', label: 'My Active Items', selected: false },
      { id: '2', label: 'High Priority', selected: true },
    ],
    compact: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Specialized filter dropdown with three sections: Filters, Group By, and Favorites.',
      },
    },
  },
};
