import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import CenteredSearchChipInput from './CenteredSearchChipInput';
import type {
  FilterTag,
  FilterItem,
  GroupByItem,
  FavoriteItem,
} from './CenteredSearchChipInput';
import { vi } from 'vitest';

// Mock the theme store
vi.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#2563eb',
      secondary: '#4f46e5',
      accent: '#7c3aed',
      warning: '#d97706',
      background: '#ffffff',
      surface: '#f9fafb',
      border: '#e5e7eb',
      text: '#111827',
      textSecondary: '#6b7280',
      textMuted: '#9ca3af',
      mutedForeground: '#64748b',
      hover: '#f1f5f9',
      shadow: 'rgba(0, 0, 0, 0.1)',
    },
  }),
}));

const mockFilterItems: FilterItem[] = [
  { id: 'status', label: 'Status' },
  { id: 'priority', label: 'Priority' },
  { id: 'custom-field', label: 'Custom Field' },
  { id: 'group-customer', label: 'Customer' },
  { id: 'fav-invoiced', label: 'Fully Invoiced' },
];

const mockFilterTags: FilterTag[] = [
  { id: '1', label: 'Active Status', removable: true },
  { id: '2', label: 'High Priority', removable: true },
];

const mockGroupByItems: GroupByItem[] = [
  { id: 'salesperson', label: 'Salesperson' },
  { id: 'customer', label: 'Customer' },
];

const mockFavoriteItems: FavoriteItem[] = [
  { id: 'fully-invoiced', label: 'Fully Invoiced' },
  { id: 'quotations', label: 'Quotations' },
];

describe('CenteredSearchChipInput', () => {
  const defaultProps = {
    placeholder: 'Search...',
    filterTags: [],
    filterItems: mockFilterItems,
    groupByItems: mockGroupByItems,
    favoriteItems: mockFavoriteItems,
    onSearch: vi.fn(),
    onTagRemove: vi.fn(),
    onFilterSelect: vi.fn(),
    onGroupBySelect: vi.fn(),
    onFavoriteSelect: vi.fn(),
    onFavoriteDelete: vi.fn(),
    onAddCustomFilter: vi.fn(),
    onAddCustomGroup: vi.fn(),
    onSaveCurrentSearch: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with placeholder text', () => {
    render(<CenteredSearchChipInput {...defaultProps} />);
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
  });

  it('renders existing filter tags', () => {
    render(
      <CenteredSearchChipInput {...defaultProps} filterTags={mockFilterTags} />
    );
    expect(screen.getByText('Active Status')).toBeInTheDocument();
    expect(screen.getByText('High Priority')).toBeInTheDocument();
  });

  it('calls onSearch when typing in input', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} />);

    const input = screen.getByPlaceholderText('Search...');
    await user.type(input, 'test query');

    expect(defaultProps.onSearch).toHaveBeenCalledWith('test query');
  });

  it('opens dropdown when input is focused', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} />);

    const input = screen.getByPlaceholderText('Search...');
    await user.click(input);

    await waitFor(() => {
      expect(screen.getByText('Filters')).toBeInTheDocument();
      expect(screen.getByText('Group By')).toBeInTheDocument();
      expect(screen.getByText('Favorites')).toBeInTheDocument();
    });
  });

  it('calls onFilterSelect when filter option is selected', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} />);

    const input = screen.getByPlaceholderText('Search...');
    await user.click(input);

    await waitFor(() => {
      expect(screen.getByText('Status')).toBeInTheDocument();
    });

    await user.click(screen.getByText('Status'));

    expect(defaultProps.onFilterSelect).toHaveBeenCalledWith('status');
  });

  it('calls onTagRemove when tag remove button is clicked', async () => {
    const user = userEvent.setup();
    render(
      <CenteredSearchChipInput {...defaultProps} filterTags={mockFilterTags} />
    );

    const removeButtons = screen.getAllByRole('button', { name: /remove/i });
    await user.click(removeButtons[0]);

    expect(defaultProps.onTagRemove).toHaveBeenCalledWith('1');
  });

  it('handles form submission', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} />);

    const input = screen.getByPlaceholderText('Search...');
    await user.type(input, 'test query');
    await user.keyboard('{Enter}');

    expect(defaultProps.onSearch).toHaveBeenCalledWith('test query');
  });

  it('applies correct accessibility attributes', () => {
    render(
      <CenteredSearchChipInput
        {...defaultProps}
        filterTags={mockFilterTags}
        data-testid="search-input"
      />
    );

    expect(screen.getByTestId('search-input')).toBeInTheDocument();

    const removeButtons = screen.getAllByRole('button', { name: /remove/i });
    expect(removeButtons[0]).toHaveAttribute(
      'aria-label',
      'Remove Active Status'
    );
  });
});
