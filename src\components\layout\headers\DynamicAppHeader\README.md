# DynamicAppHeader Component

A focused, responsive application header component designed for enterprise applications. Provides clean top navigation with app branding and user controls, optimized for both desktop and mobile experiences.

**⚠️ Breaking Change**: This component has been refactored to focus only on the top navigation bar. For bottom bar functionality (search, actions, pagination), use the new `AppDynamicContent` component.

## Features

### 🎯 Core Functionality

- **Single-row layout**: Clean top navigation focused on app branding and user controls
- **Responsive design**: Mobile-first approach with adaptive layouts
- **Theme integration**: Full integration with the application's theme system
- **Accessibility**: ARIA labels, keyboard navigation, and screen reader support

### 📱 Mobile Optimizations

- **Collapsible navigation**: Hamburger menu for mobile devices
- **Responsive user controls**: Adapts to smaller screens
- **Touch-friendly interactions**: Optimized for mobile devices

### 🎨 Visual Design

- **Dark theme optimized**: Matches the provided UI design
- **Hover effects**: Smooth transitions and interactive feedback
- **Active states**: Clear visual indication of current selections
- **Professional styling**: Enterprise-grade appearance

### 🔗 Component Integration

- **Works with AppDynamicContent**: Designed to be used alongside the new AppDynamicContent component
- **Clean separation**: Focused responsibility for better maintainability
- **Backward compatible**: Existing usage patterns still work (minus the view prop)

## Props Interface

```typescript
interface DynamicAppHeaderProps {
  app: {
    name: string;
    icon: React.ReactNode;
    navLinks: { label: string; href: string; isActive?: boolean }[];
  };
  user: {
    name: string;
    avatar: React.ReactNode;
    notifications: { count: number; icon: React.ReactNode }[];
  };

  className?: string;
  'data-testid'?: string;
}
```

**⚠️ Breaking Change**: The `view` prop has been removed. Use `AppDynamicContent` for view-related functionality (search, actions, pagination, etc.).

## Usage Example

### Basic Usage (Header Only)

```tsx
import { DynamicAppHeader } from '../components/layout';

// Sample icons (you can use any icon library)
const AppIcon = () => <svg>...</svg>;
const BellIcon = () => <svg>...</svg>;
const UserAvatar = () => (
  <div className="w-8 h-8 bg-blue-500 rounded-full">JD</div>
);

function MyApp() {
  const headerProps = {
    app: {
      name: 'Sales',
      icon: <AppIcon />,
      navLinks: [
        { label: 'Orders', href: '/orders', isActive: false },
        { label: 'Products', href: '/products', isActive: false },
        { label: 'Quotations', href: '/quotations', isActive: true },
        { label: 'Invoices', href: '/invoices', isActive: false },
      ],
    },
    user: {
      name: 'John Doe',
      avatar: <UserAvatar />,
      notifications: [{ count: 3, icon: <BellIcon /> }],
    },
  };

  return (
    <div>
      <DynamicAppHeader {...headerProps} />
      {/* Your app content */}
    </div>
  );
}
```

### Complete Layout with AppDynamicContent

```tsx
import { DynamicAppHeader, AppDynamicContent } from '../components/layout';

function CompleteApp() {
  const headerProps = {
    app: { /* app config */ },
    user: { /* user config */ },
  };

  const viewConfig = {
    title: 'Quotations',
    actions: [
      { label: 'New', onClick: () => console.log('New'), isPrimary: true },
      { label: 'Export', onClick: () => console.log('Export') },
    ],
    search: {
      onSearch: query => console.log('Search:', query),
      // ... other search config
    },
    pagination: {
      currentRange: '1-20 of 100',
      onNext: () => console.log('Next'),
      onPrev: () => console.log('Previous'),
    },
    viewModes: [
      { name: 'List', icon: <ListIcon /> },
      { name: 'Grid', icon: <GridIcon /> },
    ],
    activeViewMode: 'List',
  };

  return (
    <div className="min-h-screen">
      <DynamicAppHeader {...headerProps} />
      <AppDynamicContent view={viewConfig}>
        <YourMainContent />
      </AppDynamicContent>
    </div>
  );
}
```

## Responsive Behavior

### Desktop (≥768px)

- Full navigation menu visible
- All action buttons displayed
- Inline search with filter pills
- Complete pagination controls
- View mode switcher with all options

### Tablet (≥640px)

- Navigation menu collapses to hamburger
- Action buttons remain visible
- Search bar adapts to available space
- Pagination controls simplified

### Mobile (<640px)

- Hamburger menu for navigation
- Primary action becomes floating action button
- Search collapses to expandable overlay
- Pagination and view modes in dropdown menu

## Customization

### Theme Integration

The component automatically uses your application's theme colors through the `useThemeStore` hook:

```typescript
const { colors } = useThemeStore();
// Uses colors.primary, colors.surface, colors.text, etc.
```

### Icon Customization

All icons are passed as props, allowing you to use any icon library:

```tsx
// Using Heroicons
import { BellIcon } from '@heroicons/react/24/outline';

// Using React Icons
import { FaBell } from 'react-icons/fa';

// Using custom SVGs
const CustomIcon = () => <svg>...</svg>;
```

### Styling

The component uses Tailwind CSS classes and can be customized through:

- Theme variables (colors, spacing, etc.)
- Custom CSS classes via the `className` prop
- Inline styles for dynamic theming

## Testing

The component includes comprehensive tests covering:

- Rendering of all elements
- User interactions (clicks, form submissions)
- Responsive behavior
- Accessibility features
- Theme integration

Run tests with:

```bash
npm test DynamicAppHeader
```

## Storybook

Interactive documentation and examples are available in Storybook:

```bash
npm run storybook
```

Navigate to "Layout/DynamicAppHeader" to see all variants and interactive examples.

## Demo

A live demo is available at `/app-header-demo` in the application, showcasing:

- Interactive controls
- Real-time state changes
- Responsive behavior
- Activity logging

## Accessibility

The component follows WCAG 2.1 guidelines:

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Focus management

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- React 18+
- Tailwind CSS v4
- Theme store integration
- Button and Input components from UI library
