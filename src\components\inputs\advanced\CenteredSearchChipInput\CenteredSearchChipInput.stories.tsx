import type { Meta, StoryObj } from '@storybook/react-vite';
import CenteredSearchChipInput from './CenteredSearchChipInput';
import type {
  FilterTag,
  FilterItem,
  GroupByItem,
  FavoriteItem,
} from './CenteredSearchChipInput';
import { useState, useEffect } from 'react';
import { useThemeStore } from '../../../stores/themeStore';

const meta: Meta<typeof CenteredSearchChipInput> = {
  title: 'UI/CenteredSearchChipInput',
  component: CenteredSearchChipInput,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'An Odoo-style search and filter dropdown panel with three-column layout for Filters, Group By, and Favorites sections.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    placeholder: {
      control: 'text',
      description: 'Placeholder text for the search input',
    },
    filterTags: {
      control: 'object',
      description: 'Array of active filter tags displayed in the search bar',
    },
    filterItems: {
      control: 'object',
      description: 'Available filter items in the Filters section',
    },
    groupByItems: {
      control: 'object',
      description: 'Available grouping options in the Group By section',
    },
    favoriteItems: {
      control: 'object',
      description: 'Saved favorite searches in the Favorites section',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data for Odoo-style component
const sampleFilterTags: FilterTag[] = [
  { id: '1', label: 'Fully Invoiced', removable: true },
  { id: '2', label: 'My Quotations', removable: true },
];

const sampleFilterItems: FilterItem[] = [
  { id: 'my-quotations', label: 'My Quotations', selected: true },
  { id: 'quotations', label: 'Quotations', selected: false },
  { id: 'sales-orders', label: 'Sales Orders', selected: false },
  { id: 'create-date', label: 'Create Date', hasDropdown: true },
];

const sampleGroupByItems: GroupByItem[] = [
  { id: 'salesperson', label: 'Salesperson' },
  { id: 'customer', label: 'Customer' },
  { id: 'order-date', label: 'Order Date', hasDropdown: true },
];

const sampleFavoriteItems: FavoriteItem[] = [
  { id: 'fully-invoiced', label: 'Fully Invoiced', selected: true },
  { id: 'quotations', label: 'Quotations', selected: false },
  {
    id: 'undelivered-complete',
    label: 'Undelivered Complete',
    selected: false,
  },
  { id: 'unpaid-orders', label: 'Unpaid Orders', selected: false },
];

// Interactive story with state management
const InteractiveTemplate = (args: any) => {
  const [filterTags, setFilterTags] = useState<FilterTag[]>(
    args.filterTags || []
  );
  const [filterItems, setFilterItems] = useState<FilterItem[]>(
    args.filterItems || []
  );
  const [groupByItems, setGroupByItems] = useState<GroupByItem[]>(
    args.groupByItems || []
  );
  const [favoriteItems, setFavoriteItems] = useState<FavoriteItem[]>(
    args.favoriteItems || []
  );
  const [searchQuery, setSearchQuery] = useState('');
  const { colors, setTheme } = useThemeStore();

  // Initialize theme for Storybook
  useEffect(() => {
    setTheme('dark');
  }, [setTheme]);

  const handleTagRemove = (tagId: string) => {
    setFilterTags(prev => prev.filter(tag => tag.id !== tagId));
  };

  const handleFilterSelect = (filterId: string) => {
    setFilterItems(prev =>
      prev.map(item =>
        item.id === filterId ? { ...item, selected: !item.selected } : item
      )
    );
    console.log('Filter selected:', filterId);
  };

  const handleGroupBySelect = (groupId: string) => {
    console.log('Group by selected:', groupId);
  };

  const handleFavoriteSelect = (favoriteId: string) => {
    setFavoriteItems(prev =>
      prev.map(item =>
        item.id === favoriteId ? { ...item, selected: !item.selected } : item
      )
    );
    console.log('Favorite selected:', favoriteId);
  };

  const handleFavoriteDelete = (favoriteId: string) => {
    setFavoriteItems(prev => prev.filter(item => item.id !== favoriteId));
    console.log('Favorite deleted:', favoriteId);
  };

  const handleAddCustomFilter = () => {
    console.log('Add custom filter clicked');
  };

  const handleAddCustomGroup = () => {
    console.log('Add custom group clicked');
  };

  const handleSaveCurrentSearch = () => {
    console.log('Save current search clicked');
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    console.log('Search query:', query);
  };

  return (
    <div
      className="w-full max-w-5xl p-8 min-h-screen"
      style={{ backgroundColor: colors.background }}
    >
      <CenteredSearchChipInput
        {...args}
        filterTags={filterTags}
        filterItems={filterItems}
        groupByItems={groupByItems}
        favoriteItems={favoriteItems}
        onTagRemove={handleTagRemove}
        onFilterSelect={handleFilterSelect}
        onGroupBySelect={handleGroupBySelect}
        onFavoriteSelect={handleFavoriteSelect}
        onFavoriteDelete={handleFavoriteDelete}
        onAddCustomFilter={handleAddCustomFilter}
        onAddCustomGroup={handleAddCustomGroup}
        onSaveCurrentSearch={handleSaveCurrentSearch}
        onSearch={handleSearch}
      />

      {/* Debug Info */}
      <div
        className="mt-8 p-4 rounded-lg"
        style={{
          backgroundColor: colors.surface,
          color: colors.text,
        }}
      >
        <h3 className="font-semibold mb-2">Debug Info:</h3>
        <p>
          <strong>Search Query:</strong> {searchQuery || 'None'}
        </p>
        <p>
          <strong>Active Tags:</strong> {filterTags.length}
        </p>
        <div className="mt-2">
          <strong>Filter Tags:</strong>
          <ul className="list-disc list-inside ml-4">
            {filterTags.map(tag => (
              <li key={tag.id}>{tag.label}</li>
            ))}
          </ul>
        </div>
        <div className="mt-2">
          <strong>Selected Filters:</strong>
          <ul className="list-disc list-inside ml-4">
            {filterItems
              .filter(item => item.selected)
              .map(item => (
                <li key={item.id}>{item.label}</li>
              ))}
          </ul>
        </div>
        <div className="mt-2">
          <strong>Selected Favorites:</strong>
          <ul className="list-disc list-inside ml-4">
            {favoriteItems
              .filter(item => item.selected)
              .map(item => (
                <li key={item.id}>{item.label}</li>
              ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export const Default: Story = {
  render: InteractiveTemplate,
  args: {
    placeholder: 'Search...',
    filterTags: [],
    filterItems: sampleFilterItems,
    groupByItems: sampleGroupByItems,
    favoriteItems: sampleFavoriteItems,
  },
};

export const WithActiveTags: Story = {
  render: InteractiveTemplate,
  args: {
    placeholder: 'Search...',
    filterTags: sampleFilterTags,
    filterItems: sampleFilterItems,
    groupByItems: sampleGroupByItems,
    favoriteItems: sampleFavoriteItems,
  },
};

export const EmptyState: Story = {
  render: InteractiveTemplate,
  args: {
    placeholder: 'Search for orders, customers, products...',
    filterTags: [],
    filterItems: [],
    groupByItems: [],
    favoriteItems: [],
  },
};

export const OdooSalesExample: Story = {
  render: InteractiveTemplate,
  args: {
    placeholder: 'Search...',
    filterTags: [
      { id: '1', label: 'Fully Invoiced', removable: true },
      { id: '2', label: 'My Quotations', removable: true },
    ],
    filterItems: [
      { id: 'my-quotations', label: 'My Quotations', selected: true },
      { id: 'quotations', label: 'Quotations', selected: false },
      { id: 'sales-orders', label: 'Sales Orders', selected: false },
      { id: 'create-date', label: 'Create Date', hasDropdown: true },
    ],
    groupByItems: [
      { id: 'salesperson', label: 'Salesperson' },
      { id: 'customer', label: 'Customer' },
      { id: 'order-date', label: 'Order Date', hasDropdown: true },
    ],
    favoriteItems: [
      { id: 'fully-invoiced', label: 'Fully Invoiced', selected: true },
      { id: 'quotations', label: 'Quotations', selected: false },
      {
        id: 'undelivered-complete',
        label: 'Undelivered Complete',
        selected: false,
      },
      { id: 'unpaid-orders', label: 'Unpaid Orders', selected: false },
    ],
  },
};

export const ColoredChipsExample: Story = {
  render: InteractiveTemplate,
  args: {
    placeholder: 'Search...',
    filterTags: [
      { id: '1', label: 'Status: Active', removable: true, type: 'filter' },
      { id: '2', label: 'Group by Customer', removable: true, type: 'groupBy' },
      { id: '3', label: 'My Favorites', removable: true, type: 'favorite' },
      { id: '4', label: 'Default Tag', removable: true },
    ],
    filterItems: sampleFilterItems,
    groupByItems: sampleGroupByItems,
    favoriteItems: sampleFavoriteItems,
  },
};
