// Hook return types for user management

import type { User, UserFilters, UserFormData, UserFormErrors, CreateUserRequest, UpdateUserRequest } from '../types';

export interface UseUsersReturn {
  users: User[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  mutate: (data?: User[]) => Promise<User[] | undefined>;
}

export interface UseUserFormReturn {
  formData: UserFormData;
  errors: UserFormErrors;
  isSubmitting: boolean;
  isValid: boolean;
  handleChange: (field: keyof UserFormData, value: string) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  reset: () => void;
  setErrors: (errors: UserFormErrors) => void;
}

export interface UseUserFiltersReturn {
  filters: UserFilters;
  setFilter: <K extends keyof UserFilters>(key: K, value: UserFilters[K]) => void;
  clearFilters: () => void;
  hasActiveFilters: boolean;
}

export interface UseUserActionsReturn {
  createUser: (data: CreateUserRequest) => Promise<User>;
  updateUser: (data: UpdateUserRequest) => Promise<User>;
  deleteUser: (id: string) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}
