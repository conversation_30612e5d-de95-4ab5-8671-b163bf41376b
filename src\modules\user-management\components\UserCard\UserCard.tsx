import React from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import type { User } from '../../types';

export interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
  onDelete?: (user: User) => void;
  showActions?: boolean;
  className?: string;
  'data-testid'?: string;
}

export const UserCard: React.FC<UserCardProps> = ({
  user,
  onEdit,
  onDelete,
  showActions = true,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return '#ef4444'; // red
      case 'moderator':
        return '#f59e0b'; // amber
      case 'viewer':
        return '#6b7280'; // gray
      default:
        return colors.secondary;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div
      className={`bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-4 hover:shadow-md transition-shadow ${className}`}
      data-testid={testId}
    >
      <div className="flex items-center space-x-3">
        <div
          className="w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold"
          style={{ backgroundColor: colors.primary }}
        >
          {user.name.charAt(0).toUpperCase()}
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100 truncate">
            {user.name}
          </h3>
          <p className="text-sm text-slate-500 dark:text-slate-400 truncate">
            {user.email}
          </p>
        </div>
      </div>

      <div className="mt-3 flex items-center justify-between">
        <span
          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
          style={{
            backgroundColor: `${getRoleColor(user.role)}20`,
            color: getRoleColor(user.role),
          }}
        >
          {user.role}
        </span>
        {user.createdAt && (
          <span className="text-xs text-slate-400 dark:text-slate-500">
            {formatDate(user.createdAt)}
          </span>
        )}
      </div>

      {showActions && (onEdit || onDelete) && (
        <div className="mt-3 flex items-center justify-end space-x-2">
          {onEdit && (
            <button
              onClick={() => onEdit(user)}
              className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 font-medium"
              data-testid="user-card-edit"
            >
              Edit
            </button>
          )}
          {onDelete && (
            <button
              onClick={() => onDelete(user)}
              className="text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 font-medium"
              data-testid="user-card-delete"
            >
              Delete
            </button>
          )}
        </div>
      )}
    </div>
  );
};
