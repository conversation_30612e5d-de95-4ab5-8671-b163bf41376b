// Layout Components - Structural components for page layout
// These components handle the overall structure and positioning of content

// Headers - Top navigation and app bars
export * from './headers';

// Content - Main content areas and dynamic content
export * from './content';

// Containers - Layout containers and structural elements
export * from './containers';

// Sidebars - Side navigation and panels
export * from './sidebars';

// Responsive - Responsive layout components
export * from './responsive';

// Direct exports for backward compatibility
export { default as DynamicAppBottomBar } from './DynamicAppBottomBar';
export type { DynamicAppBottomBarProps } from './DynamicAppBottomBar';

// Layout Types - Common types used across layout components
export type {
  LayoutVariant,
  LayoutSize,
  ResponsiveBreakpoint,
  LayoutDirection,
  LayoutAlignment,
  LayoutSpacing,
} from '../types/layout';

// TODO: Future layout components to implement
// - AppLayout: Main application layout wrapper
// - PageLayout: Individual page layout container
// - Footer: Application footer component
// - Grid: Flexible grid system
// - Flex: Flexbox layout utilities
// - Stack: Vertical/horizontal stacking component
// - Center: Centering layout component
// - Spacer: Spacing utility component
