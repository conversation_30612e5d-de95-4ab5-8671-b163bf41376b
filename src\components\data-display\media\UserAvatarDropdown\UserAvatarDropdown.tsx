import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import Dropdown from '../Dropdown/Dropdown';
import {
  SettingsIcon,
  LogOutIcon,
  BookIcon,
  KeyboardIcon,
  DownloadIcon,
  PlayIcon,
} from '../../icons';
import type { DropdownItem } from '../Dropdown/Dropdown';

export interface UserAvatarDropdownProps {
  user: {
    name: string;
    avatar: React.ReactNode;
    email?: string;
  };
  onPreferences?: () => void;
  onLogout?: () => void;
  onDocumentation?: () => void;
  onShortcuts?: () => void;
  onInstallApp?: () => void;
  onOnboarding?: () => void;
  className?: string;
  'data-testid'?: string;
}

const UserAvatarDropdown: React.FC<UserAvatarDropdownProps> = ({
  user,
  onPreferences,
  onLogout,
  onDocumentation,
  onShortcuts,
  onInstallApp,
  onOnboarding,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const dropdownItems: DropdownItem[] = [
    {
      id: 'preferences',
      label: 'Preferences',
      icon: <SettingsIcon className="w-4 h-4" />,
      onClick: onPreferences || (() => console.log('Open preferences')),
      description: 'Account and app settings',
    },
    {
      id: 'divider-1',
      label: '',
      isDivider: true,
    },
    {
      id: 'documentation',
      label: 'Documentation',
      icon: <BookIcon className="w-4 h-4" />,
      onClick: onDocumentation || (() => console.log('Open documentation')),
      description: 'Help and guides',
    },
    {
      id: 'shortcuts',
      label: 'Shortcuts',
      icon: <KeyboardIcon className="w-4 h-4" />,
      onClick: onShortcuts || (() => console.log('Show shortcuts')),
      shortcut: 'Ctrl+K',
      description: 'Keyboard shortcuts',
    },
    {
      id: 'divider-2',
      label: '',
      isDivider: true,
    },
    {
      id: 'install-app',
      label: 'Install App',
      icon: <DownloadIcon className="w-4 h-4" />,
      onClick: onInstallApp || (() => console.log('Install app')),
      description: 'Install as PWA',
    },
    {
      id: 'onboarding',
      label: 'Onboarding / Tour',
      icon: <PlayIcon className="w-4 h-4" />,
      onClick: onOnboarding || (() => console.log('Start onboarding')),
      description: 'Take a guided tour',
    },
    {
      id: 'divider-3',
      label: '',
      isDivider: true,
    },
    {
      id: 'logout',
      label: 'Logout',
      icon: <LogOutIcon className="w-4 h-4" />,
      onClick: onLogout || (() => console.log('Logout')),
    },
  ];

  const trigger = (
    <div
      className={`
        flex items-center space-x-2 p-2 rounded-xl
        transition-all duration-200 cursor-pointer group
        hover:scale-105 hover:shadow-lg
        ${className}
      `}
      style={{
        backgroundColor: 'transparent',
        border: `1px solid transparent`,
      }}
      onMouseEnter={e => {
        e.currentTarget.style.backgroundColor = `${colors.hover}20`;
        e.currentTarget.style.borderColor = `${colors.primary}30`;
        e.currentTarget.style.boxShadow = `0 4px 12px ${colors.primary}15`;
      }}
      onMouseLeave={e => {
        e.currentTarget.style.backgroundColor = 'transparent';
        e.currentTarget.style.borderColor = 'transparent';
        e.currentTarget.style.boxShadow = 'none';
      }}
    >
      {/* User Avatar with enhanced styling */}
      <div className="relative">
        <div
          className="w-9 h-9 rounded-xl overflow-hidden flex items-center justify-center ring-2 ring-transparent transition-all duration-200 group-hover:ring-primary/30"
          style={{
            background: `linear-gradient(135deg, ${colors.primary}15, ${colors.primary}05)`,
          }}
        >
          {user.avatar}
        </div>

        {/* Online status indicator */}
        <div
          className="absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 transition-all duration-200"
          style={{
            backgroundColor: '#10B981', // Green for online
            borderColor: colors.background,
            opacity: 0.9,
          }}
        />
      </div>

      {/* User name (optional, can be shown on larger screens) */}
      <div className="hidden lg:block">
        <div
          className="text-sm font-medium transition-colors duration-200 group-hover:text-primary"
          style={{ color: colors.text }}
        >
          {user.name.split(' ')[0]} {/* Show first name only */}
        </div>
        {user.email && (
          <div
            className="text-xs transition-colors duration-200"
            style={{ color: colors.mutedForeground }}
          >
            {user.email.length > 20
              ? `${user.email.substring(0, 20)}...`
              : user.email}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <Dropdown
      trigger={trigger}
      items={dropdownItems}
      align="right"
      className={className}
      dropdownClassName="min-w-[240px]"
      data-testid={testId}
    />
  );
};

export default UserAvatarDropdown;
