import React, { useRef, useEffect, createContext, useContext } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import type { BaseDropdownProps } from './types';

// Context for dropdown state
interface DropdownContextValue {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  closeOnSelect: boolean;
  size: 'small' | 'medium' | 'large';
  variant: 'default' | 'menu' | 'filter' | 'select';
}

const DropdownContext = createContext<DropdownContextValue | null>(null);

export const useDropdownContext = () => {
  const context = useContext(DropdownContext);
  if (!context) {
    throw new Error('Dropdown components must be used within a Dropdown');
  }
  return context;
};

/**
 * Base dropdown component that provides the foundation for all dropdown variants
 * Uses composition pattern for maximum flexibility
 */
export const DropdownBase: React.FC<BaseDropdownProps & { children: React.ReactNode }> = ({
  isOpen,
  onOpenChange,
  align = 'right',
  offset = 8,
  size = 'medium',
  variant = 'default',
  width,
  maxHeight,
  closeOnSelect = true,
  closeOnClickOutside = true,
  closeOnEscape = true,
  className = '',
  contentClassName = '',
  'aria-label': ariaLabel,
  'data-testid': testId,
  children,
}) => {
  const { colors } = useThemeStore();
  const containerRef = useRef<HTMLDivElement>(null);

  // Close on click outside
  useEffect(() => {
    if (!closeOnClickOutside || !isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        onOpenChange(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, closeOnClickOutside, onOpenChange]);

  // Close on escape key
  useEffect(() => {
    if (!closeOnEscape || !isOpen) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onOpenChange(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onOpenChange]);

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          content: 'text-xs',
          padding: 'p-1',
          minWidth: 'min-w-[150px]',
        };
      case 'large':
        return {
          content: 'text-base',
          padding: 'p-3',
          minWidth: 'min-w-[250px]',
        };
      default: // medium
        return {
          content: 'text-sm',
          padding: 'p-2',
          minWidth: 'min-w-[200px]',
        };
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'menu':
        return {
          backgroundColor: colors.surface,
          borderColor: colors.border,
          boxShadow: `0 10px 25px -5px ${colors.shadow}40, 0 4px 6px -2px ${colors.shadow}20`,
        };
      case 'filter':
        return {
          backgroundColor: colors.surface,
          borderColor: colors.border,
          boxShadow: `0 4px 6px -1px ${colors.shadow}`,
        };
      case 'select':
        return {
          backgroundColor: colors.background,
          borderColor: colors.border,
          boxShadow: `0 2px 4px -1px ${colors.shadow}20`,
        };
      default:
        return {
          backgroundColor: colors.surface,
          borderColor: colors.border,
          boxShadow: `0 4px 6px -1px ${colors.shadow}30`,
        };
    }
  };

  const sizeClasses = getSizeClasses();
  const variantStyles = getVariantStyles();

  const alignmentClasses = {
    left: 'left-0',
    right: 'right-0',
    center: 'left-1/2 transform -translate-x-1/2',
  };

  const contextValue: DropdownContextValue = {
    isOpen,
    onOpenChange,
    closeOnSelect,
    size,
    variant,
  };

  return (
    <DropdownContext.Provider value={contextValue}>
      <div
        ref={containerRef}
        className={cn('relative', className)}
        data-testid={testId}
        role="combobox"
        aria-expanded={isOpen}
        aria-label={ariaLabel}
      >
        {children}
        
        {/* Dropdown Content */}
        {isOpen && (
          <div
            className={cn(
              'absolute z-50 rounded-lg border',
              sizeClasses.content,
              sizeClasses.padding,
              sizeClasses.minWidth,
              alignmentClasses[align],
              contentClassName
            )}
            style={{
              top: `calc(100% + ${offset}px)`,
              width: width || 'auto',
              maxHeight: maxHeight || 'auto',
              ...variantStyles,
            }}
            role="listbox"
          >
            <div className="overflow-y-auto max-h-full">
              {children}
            </div>
          </div>
        )}
      </div>
    </DropdownContext.Provider>
  );
};
