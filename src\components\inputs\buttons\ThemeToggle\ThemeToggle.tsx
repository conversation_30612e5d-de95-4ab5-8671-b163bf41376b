import React, { useState } from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { SunIcon, MoonIcon, SystemIcon } from '../../icons';
import type { Theme } from '../../../stores/themeStore';

export interface ThemeToggleProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  'data-testid'?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'md',
  showLabel = false,
  'data-testid': testId,
}) => {
  const { theme, setTheme, colors } = useThemeStore();
  const [isAnimating, setIsAnimating] = useState(false);

  const themes: Array<{
    value: Theme;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
  }> = [
    { value: 'light', label: 'Light Mode', icon: SunIcon },
    { value: 'dark', label: 'Dark Mode', icon: MoonIcon },
    { value: 'system', label: 'System', icon: SystemIcon },
  ];

  const currentThemeIndex = themes.findIndex(t => t.value === theme);
  const currentTheme = themes[currentThemeIndex];

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  const handleToggle = async () => {
    if (isAnimating) return;

    setIsAnimating(true);

    // Cycle through themes: light -> dark -> system -> light
    const nextIndex = (currentThemeIndex + 1) % themes.length;
    const nextTheme = themes[nextIndex];

    // Add a small delay for animation effect
    setTimeout(() => {
      setTheme(nextTheme.value);
      setIsAnimating(false);
    }, 150);
  };

  const IconComponent = currentTheme.icon;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {showLabel && (
        <span className="text-sm font-medium" style={{ color: colors.text }}>
          {currentTheme.label}
        </span>
      )}

      <button
        onClick={handleToggle}
        className={`
          ${sizeClasses[size]}
          relative
          rounded-lg
          transition-all
          duration-300
          ease-in-out
          hover:scale-105
          hover:bg-opacity-10
          active:scale-95
          focus:outline-none
          focus:ring-2
          focus:ring-offset-2
          disabled:opacity-50
          disabled:cursor-not-allowed
          disabled:hover:scale-100
        `}
        style={
          {
            backgroundColor: 'transparent',
            color: colors.text,
            '--focus-ring': colors.primary,
          } as React.CSSProperties & { '--focus-ring': string }
        }
        disabled={isAnimating}
        aria-label={`Switch to next theme (current: ${currentTheme.label})`}
        title={`Current: ${currentTheme.label}. Click to cycle themes.`}
        data-testid={testId}
      >
        <div
          className={`
            ${iconSizeClasses[size]}
            absolute
            inset-0
            m-auto
            transform
            transition-all
            duration-300
            ease-in-out
            ${isAnimating ? 'scale-75 rotate-180 opacity-0' : 'scale-100 rotate-0 opacity-100'}
          `}
          style={{ color: colors.text }}
        >
          <IconComponent className="w-full h-full" />
        </div>

        {/* Ripple effect on click */}
        <div
          className={`
            absolute
            inset-0
            rounded-lg
            transition-all
            duration-300
            ease-out
            ${isAnimating ? 'scale-150 opacity-20' : 'scale-0 opacity-0'}
          `}
          style={{ backgroundColor: colors.primary }}
        />
      </button>
    </div>
  );
};

export default ThemeToggle;
