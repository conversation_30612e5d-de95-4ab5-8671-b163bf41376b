import React from 'react';
import { cn } from '../../../utils/cn';
import { useDropdownContext } from './DropdownBase';
import type { DropdownTriggerProps } from './types';

/**
 * Dropdown trigger component that handles opening/closing the dropdown
 */
export const DropdownTrigger: React.FC<DropdownTriggerProps> = ({
  children,
  disabled = false,
  className = '',
  asChild = false,
}) => {
  const { isOpen, onOpenChange } = useDropdownContext();

  const handleClick = () => {
    if (!disabled) {
      onOpenChange(!isOpen);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick();
    }
  };

  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children, {
      onClick: handleClick,
      onKeyDown: handleKeyDown,
      'aria-expanded': isOpen,
      'aria-haspopup': true,
      disabled,
      className: cn(children.props.className, className),
    });
  }

  return (
    <button
      type="button"
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled}
      className={cn(
        'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      aria-expanded={isOpen}
      aria-haspopup={true}
    >
      {children}
    </button>
  );
};
