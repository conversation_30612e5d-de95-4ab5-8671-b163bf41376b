// Layout Component Types
// Common types and interfaces used across layout components

export type LayoutVariant = 'default' | 'compact' | 'minimal' | 'expanded';
export type LayoutSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
export type LayoutDirection = 'row' | 'column' | 'row-reverse' | 'column-reverse';
export type LayoutAlignment = 'start' | 'center' | 'end' | 'stretch' | 'baseline';
export type LayoutJustification = 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';

export type ResponsiveBreakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

export interface ResponsiveValue<T> {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
}

export type LayoutSpacing = 
  | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12
  | 14 | 16 | 20 | 24 | 28 | 32 | 36 | 40 | 44 | 48 | 52 | 56 | 60 | 64
  | 72 | 80 | 96;

export interface BaseLayoutProps {
  className?: string;
  'data-testid'?: string;
}

export interface ResponsiveLayoutProps extends BaseLayoutProps {
  breakpoint?: ResponsiveBreakpoint;
  responsive?: boolean;
}

export interface SpacingProps {
  padding?: LayoutSpacing | ResponsiveValue<LayoutSpacing>;
  margin?: LayoutSpacing | ResponsiveValue<LayoutSpacing>;
  gap?: LayoutSpacing | ResponsiveValue<LayoutSpacing>;
}

export interface FlexLayoutProps extends BaseLayoutProps, SpacingProps {
  direction?: LayoutDirection | ResponsiveValue<LayoutDirection>;
  align?: LayoutAlignment | ResponsiveValue<LayoutAlignment>;
  justify?: LayoutJustification | ResponsiveValue<LayoutJustification>;
  wrap?: boolean | ResponsiveValue<boolean>;
  flex?: string | number | ResponsiveValue<string | number>;
}

export interface GridLayoutProps extends BaseLayoutProps, SpacingProps {
  columns?: number | ResponsiveValue<number>;
  rows?: number | ResponsiveValue<number>;
  autoFit?: boolean;
  autoFill?: boolean;
  minColumnWidth?: string;
}

export interface ContainerProps extends BaseLayoutProps, SpacingProps {
  maxWidth?: LayoutSize | ResponsiveValue<LayoutSize>;
  centered?: boolean;
  fluid?: boolean;
}

export interface SectionProps extends BaseLayoutProps, SpacingProps {
  as?: keyof JSX.IntrinsicElements;
  variant?: LayoutVariant;
  fullHeight?: boolean;
}

// Header and Navigation Types
export interface HeaderProps extends BaseLayoutProps {
  variant?: 'default' | 'compact' | 'minimal';
  sticky?: boolean;
  transparent?: boolean;
  bordered?: boolean;
  height?: LayoutSize;
}

export interface SidebarProps extends BaseLayoutProps {
  variant?: 'default' | 'compact' | 'overlay';
  position?: 'left' | 'right';
  collapsible?: boolean;
  collapsed?: boolean;
  width?: LayoutSize | string;
  overlay?: boolean;
}

// Content Area Types
export interface ContentAreaProps extends BaseLayoutProps, SpacingProps {
  variant?: LayoutVariant;
  scrollable?: boolean;
  fullHeight?: boolean;
}

export interface MainContentProps extends ContentAreaProps {
  withSidebar?: boolean;
  sidebarPosition?: 'left' | 'right';
}

// Layout Context Types
export interface LayoutContextValue {
  breakpoint: ResponsiveBreakpoint;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
}

// Utility Types
export type LayoutComponent<P = {}> = React.FC<P & BaseLayoutProps>;
export type ResponsiveLayoutComponent<P = {}> = React.FC<P & ResponsiveLayoutProps>;
