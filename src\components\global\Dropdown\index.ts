// Enhanced Dropdown Components
// Composition-based dropdown system with flexible building blocks

// Main dropdown component
export { Dropdown } from './Dropdown';
export type { DropdownProps, DropdownWithSectionsProps } from './Dropdown';

// Filter dropdown component
export { FilterDropdown } from './FilterDropdown';
export type {
  FilterDropdownProps,
  FilterItem,
  GroupByItem,
  FavoriteItem
} from './FilterDropdown';

// Individual components for advanced composition
export {
  DropdownBase,
  DropdownTrigger,
  DropdownContent,
  DropdownItem,
  DropdownSection,
  DropdownSeparator,
} from './Dropdown';

// Context hook
export { useDropdownContext } from './DropdownBase';

// Types
export type {
  BaseDropdownProps,
  DropdownItem as DropdownItemType,
  DropdownSection as DropdownSectionType,
  DropdownAlign,
  DropdownSize,
  DropdownVariant,
  DropdownTriggerProps,
  DropdownContentProps,
  DropdownItemProps,
  DropdownSectionProps,
  DropdownSeparatorProps,
} from './types';
