import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Heading } from './Heading';

const meta: Meta<typeof Heading> = {
  title: 'Global/Heading',
  component: Heading,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A flexible heading component that provides consistent heading styles across the application with semantic HTML elements.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    level: {
      control: { type: 'select' },
      options: [1, 2, 3, 4, 5, 6],
      description: 'Heading level (h1-h6)',
    },
    size: {
      control: { type: 'select' },
      options: ['xs', 'sm', 'base', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl'],
      description: 'Visual size (independent of semantic level)',
    },
    weight: {
      control: { type: 'select' },
      options: ['normal', 'medium', 'semibold', 'bold', 'extrabold'],
      description: 'Font weight',
    },
    color: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'muted', 'error', 'warning', 'success', 'info', 'inherit'],
      description: 'Text color',
    },
    align: {
      control: { type: 'select' },
      options: ['left', 'center', 'right'],
      description: 'Text alignment',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default heading
export const Default: Story = {
  args: {
    level: 2,
    children: 'Default Heading',
  },
};

// All heading levels
export const Levels: Story = {
  render: () => (
    <div className="space-y-4">
      <Heading level={1}>Heading Level 1</Heading>
      <Heading level={2}>Heading Level 2</Heading>
      <Heading level={3}>Heading Level 3</Heading>
      <Heading level={4}>Heading Level 4</Heading>
      <Heading level={5}>Heading Level 5</Heading>
      <Heading level={6}>Heading Level 6</Heading>
    </div>
  ),
};

// Different sizes (independent of level)
export const Sizes: Story = {
  render: () => (
    <div className="space-y-4">
      <Heading level={3} size="xs">Extra Small Heading</Heading>
      <Heading level={3} size="sm">Small Heading</Heading>
      <Heading level={3} size="base">Base Heading</Heading>
      <Heading level={3} size="lg">Large Heading</Heading>
      <Heading level={3} size="xl">Extra Large Heading</Heading>
      <Heading level={3} size="2xl">2X Large Heading</Heading>
      <Heading level={3} size="3xl">3X Large Heading</Heading>
      <Heading level={3} size="4xl">4X Large Heading</Heading>
      <Heading level={3} size="5xl">5X Large Heading</Heading>
      <Heading level={3} size="6xl">6X Large Heading</Heading>
    </div>
  ),
};

// Font weights
export const Weights: Story = {
  render: () => (
    <div className="space-y-4">
      <Heading level={2} weight="normal">Normal Weight</Heading>
      <Heading level={2} weight="medium">Medium Weight</Heading>
      <Heading level={2} weight="semibold">Semibold Weight</Heading>
      <Heading level={2} weight="bold">Bold Weight</Heading>
      <Heading level={2} weight="extrabold">Extra Bold Weight</Heading>
    </div>
  ),
};

// Colors
export const Colors: Story = {
  render: () => (
    <div className="space-y-4">
      <Heading level={2} color="primary">Primary Color</Heading>
      <Heading level={2} color="secondary">Secondary Color</Heading>
      <Heading level={2} color="muted">Muted Color</Heading>
      <Heading level={2} color="error">Error Color</Heading>
      <Heading level={2} color="warning">Warning Color</Heading>
      <Heading level={2} color="success">Success Color</Heading>
      <Heading level={2} color="info">Info Color</Heading>
    </div>
  ),
};

// Text alignment
export const Alignment: Story = {
  render: () => (
    <div className="space-y-4 w-full">
      <Heading level={2} align="left">Left Aligned Heading</Heading>
      <Heading level={2} align="center">Center Aligned Heading</Heading>
      <Heading level={2} align="right">Right Aligned Heading</Heading>
    </div>
  ),
};

// Truncated headings
export const Truncated: Story = {
  render: () => (
    <div className="space-y-4 w-64">
      <Heading level={2} truncate>
        This is a very long heading that will be truncated
      </Heading>
      <Heading level={2} lineClamp={2}>
        This is a longer heading that will be clamped to exactly two lines when it exceeds the container width
      </Heading>
    </div>
  ),
};

// Semantic vs Visual
export const SemanticVsVisual: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <p className="text-sm text-gray-600 mb-2">H1 with H3 visual size:</p>
        <Heading level={1} size="2xl">Semantically H1, Visually H3</Heading>
      </div>
      <div>
        <p className="text-sm text-gray-600 mb-2">H3 with H1 visual size:</p>
        <Heading level={3} size="4xl">Semantically H3, Visually H1</Heading>
      </div>
    </div>
  ),
};

// Page hierarchy example
export const PageHierarchy: Story = {
  render: () => (
    <div className="space-y-6 max-w-2xl">
      <Heading level={1} size="4xl">Page Title</Heading>
      <div className="space-y-4">
        <Heading level={2} size="2xl">Section Heading</Heading>
        <p className="text-gray-600">
          This is some content under the section heading. It provides context 
          and information related to the section.
        </p>
        <div className="space-y-2">
          <Heading level={3} size="xl">Subsection Heading</Heading>
          <p className="text-gray-600">
            This is content under a subsection. It's more specific information 
            that relates to the broader section above.
          </p>
          <Heading level={4} size="lg">Sub-subsection Heading</Heading>
          <p className="text-gray-600">
            Even more specific content that drills down into the details.
          </p>
        </div>
      </div>
    </div>
  ),
};
