import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import AppDynamicContent from './AppDynamicContent';
import type { AppDynamicContentProps } from './AppDynamicContent';

// Mock the theme store
jest.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      background: '#ffffff',
      surface: '#f8f9fa',
      border: '#e9ecef',
      text: '#212529',
      textSecondary: '#6c757d',
      mutedForeground: '#6c757d',
      primary: '#0d6efd',
      primaryForeground: '#ffffff',
      hover: '#e9ecef',
      error: '#dc3545',
      errorForeground: '#ffffff',
    },
  }),
}));

// Mock DynamicAppBottomBar
jest.mock('../DynamicAppBottomBar/DynamicAppBottomBar', () => {
  return function MockDynamicAppBottomBar({ view }: any) {
    return (
      <div data-testid="mock-bottom-bar">
        <div data-testid="bottom-bar-title">{view.title}</div>
        {view.actions.map((action: any, index: number) => (
          <button key={index} data-testid={`action-${action.label.toLowerCase()}`}>
            {action.label}
          </button>
        ))}
      </div>
    );
  };
});

const mockView = {
  title: 'Test Dashboard',
  actions: [
    { label: 'New', onClick: jest.fn(), isPrimary: true },
    { label: 'Export', onClick: jest.fn() },
  ],
  search: {
    onSearch: jest.fn(),
    filters: [],
    onRemoveFilter: jest.fn(),
  },
  pagination: {
    currentRange: '1-20 of 100',
    onNext: jest.fn(),
    onPrev: jest.fn(),
  },
  viewModes: [
    { name: 'List', icon: '📋' },
    { name: 'Grid', icon: '⊞' },
  ],
  activeViewMode: 'List',
};

const defaultProps: AppDynamicContentProps = {
  view: mockView,
  children: <div data-testid="test-content">Test Content</div>,
};

const renderWithRouter = (component: React.ReactElement) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('AppDynamicContent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    renderWithRouter(<AppDynamicContent {...defaultProps} />);
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
  });

  it('renders the DynamicAppBottomBar with correct view config', () => {
    renderWithRouter(<AppDynamicContent {...defaultProps} />);
    
    expect(screen.getByTestId('mock-bottom-bar')).toBeInTheDocument();
    expect(screen.getByTestId('bottom-bar-title')).toHaveTextContent('Test Dashboard');
    expect(screen.getByTestId('action-new')).toBeInTheDocument();
    expect(screen.getByTestId('action-export')).toBeInTheDocument();
  });

  it('renders children content in the main area', () => {
    const customContent = (
      <div data-testid="custom-content">
        <h1>Custom Title</h1>
        <p>Custom paragraph</p>
      </div>
    );

    renderWithRouter(
      <AppDynamicContent {...defaultProps}>
        {customContent}
      </AppDynamicContent>
    );

    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
    expect(screen.getByText('Custom paragraph')).toBeInTheDocument();
  });

  it('applies custom className to container', () => {
    const { container } = renderWithRouter(
      <AppDynamicContent {...defaultProps} className="custom-container" />
    );

    const containerDiv = container.firstChild as HTMLElement;
    expect(containerDiv).toHaveClass('custom-container');
  });

  it('applies custom contentClassName to main content area', () => {
    renderWithRouter(
      <AppDynamicContent 
        {...defaultProps} 
        contentClassName="custom-content-class"
        data-testid="app-content"
      />
    );

    const mainElement = screen.getByRole('main');
    expect(mainElement).toHaveClass('custom-content-class');
  });

  it('applies data-testid when provided', () => {
    renderWithRouter(
      <AppDynamicContent {...defaultProps} data-testid="app-dynamic-content" />
    );

    expect(screen.getByTestId('app-dynamic-content')).toBeInTheDocument();
  });

  it('has correct semantic structure', () => {
    renderWithRouter(<AppDynamicContent {...defaultProps} />);

    // Should have a main element for the content area
    const mainElement = screen.getByRole('main');
    expect(mainElement).toBeInTheDocument();
    expect(mainElement).toHaveClass('flex-1');
  });

  it('applies responsive classes correctly', () => {
    renderWithRouter(<AppDynamicContent {...defaultProps} />);

    const mainElement = screen.getByRole('main');
    expect(mainElement).toHaveClass('max-w-7xl', 'mx-auto', 'px-4', 'sm:px-6', 'lg:px-8', 'py-8');
  });

  it('handles complex children content', () => {
    const complexContent = (
      <div data-testid="complex-content">
        <header>
          <h1>Page Title</h1>
          <nav>Navigation</nav>
        </header>
        <section>
          <article>Article content</article>
          <aside>Sidebar content</aside>
        </section>
        <footer>Footer content</footer>
      </div>
    );

    renderWithRouter(
      <AppDynamicContent {...defaultProps}>
        {complexContent}
      </AppDynamicContent>
    );

    expect(screen.getByTestId('complex-content')).toBeInTheDocument();
    expect(screen.getByText('Page Title')).toBeInTheDocument();
    expect(screen.getByText('Navigation')).toBeInTheDocument();
    expect(screen.getByText('Article content')).toBeInTheDocument();
    expect(screen.getByText('Sidebar content')).toBeInTheDocument();
    expect(screen.getByText('Footer content')).toBeInTheDocument();
  });

  it('maintains proper layout structure', () => {
    const { container } = renderWithRouter(<AppDynamicContent {...defaultProps} />);

    const containerDiv = container.firstChild as HTMLElement;
    expect(containerDiv).toHaveClass('flex', 'flex-col', 'min-h-0');

    const mainElement = screen.getByRole('main');
    expect(mainElement).toHaveClass('flex-1');
  });
});
