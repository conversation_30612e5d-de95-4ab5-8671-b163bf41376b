import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import { LoadingIcon } from '../../icons';
import type { BaseFormProps } from '../types';

export interface ButtonProps extends Omit<BaseFormProps, 'variant'> {
  children: React.ReactNode;
  variant?:
    | 'primary'
    | 'secondary'
    | 'outline'
    | 'ghost'
    | 'destructive'
    | 'danger'
    | 'link';
  loading?: boolean;
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

/**
 * Enhanced Button component for forms
 * Moved from ui/Button with form-specific enhancements
 */
export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  rounded = 'md',
  shadow = 'sm',
  onClick,
  type = 'button',
  startIcon,
  endIcon,
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();

  const baseClasses = cn(
    'inline-flex items-center justify-center font-medium transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
    'active:scale-95 transform-gpu',
    fullWidth && 'w-full'
  );

  const sizeClasses = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 text-base',
    lg: 'h-12 px-6 text-lg',
  };

  const roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    full: 'rounded-full',
  };

  const shadowClasses = {
    none: 'shadow-none',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: colors.primary,
          color: colors.primaryForeground,
          borderColor: colors.primary,
        };
      case 'secondary':
        return {
          backgroundColor: colors.secondary,
          color: colors.secondaryForeground,
          borderColor: colors.secondary,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          color: colors.primary,
          borderColor: colors.border,
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          color: colors.text,
          borderColor: 'transparent',
        };
      case 'destructive':
      case 'danger':
        return {
          backgroundColor: colors.error,
          color: colors.errorForeground,
          borderColor: colors.error,
        };
      case 'link':
        return {
          backgroundColor: 'transparent',
          color: colors.primary,
          borderColor: 'transparent',
        };
      default:
        return {
          backgroundColor: colors.primary,
          color: colors.primaryForeground,
          borderColor: colors.primary,
        };
    }
  };

  const variantStyles = getVariantStyles();

  const variantClasses = {
    primary: 'border hover:opacity-90',
    secondary: 'border hover:opacity-90',
    outline: 'border hover:bg-opacity-10',
    ghost: 'border-0 hover:bg-opacity-10',
    destructive: 'border hover:opacity-90',
    danger: 'border hover:opacity-90',
    link: 'border-0 hover:underline shadow-none',
  };

  const buttonClasses = cn(
    baseClasses,
    sizeClasses[size],
    roundedClasses[rounded],
    shadowClasses[shadow],
    variantClasses[variant],
    loading && 'cursor-wait',
    className
  );

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled && !loading && onClick) {
      onClick(event);
    }
  };

  return (
    <button
      type={type}
      className={buttonClasses}
      style={variantStyles}
      disabled={disabled || loading}
      onClick={handleClick}
      data-testid={testId}
    >
      {loading ? (
        <LoadingIcon
          className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
          aria-hidden={true}
        />
      ) : (
        startIcon && (
          <span className="-ml-1 mr-2 h-4 w-4" aria-hidden={true}>
            {startIcon}
          </span>
        )
      )}
      {children}
      {endIcon && !loading && (
        <span className="ml-2 -mr-1 h-4 w-4" aria-hidden={true}>
          {endIcon}
        </span>
      )}
    </button>
  );
};
