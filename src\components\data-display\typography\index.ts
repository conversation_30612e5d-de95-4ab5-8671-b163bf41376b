// Typography Components
// Text, headings, and content display

// Core Typography
export { default as Text } from './Text';
export type { TextProps } from './Text';

export { default as Caption } from './Caption';
export type { CaptionProps } from './Caption';

// TODO: Add Heading when available
// export { default as Heading } from './Heading';
// export type { HeadingProps } from './Heading';

// Re-export from global for enhanced typography
export { Text as GlobalText } from '../../global/Text';
export type { TextProps as GlobalTextProps } from '../../global/Text';

export { Heading as GlobalHeading } from '../../global/Heading';
export type { HeadingProps as GlobalHeadingProps } from '../../global/Heading';

// TODO: Add more typography components
// export { default as Code } from './Code';
// export { default as Blockquote } from './Blockquote';
// export { default as Link } from './Link';
