import type { <PERSON>a, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { ViewModeSelector } from './ViewModeSelector';
import { dataViewModes, simpleViewModes, discussViewModes, tableViewModes } from './presets';
import { useThemeStore } from '../../../stores/themeStore';

const meta: Meta<typeof ViewModeSelector> = {
  title: 'Global/ViewModeSelector',
  component: ViewModeSelector,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A unified view mode selector that consolidates all view switching patterns. Supports toggle, switcher, selector, and compact variants.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['toggle', 'switcher', 'selector', 'compact'],
      description: 'Visual variant of the selector',
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
      description: 'Size of the selector',
    },
    orientation: {
      control: { type: 'select' },
      options: ['horizontal', 'vertical'],
      description: 'Layout orientation',
    },
    showLabels: {
      control: 'boolean',
      description: 'Show text labels alongside icons',
    },
    showDescriptions: {
      control: 'boolean',
      description: 'Show descriptions (selector variant only)',
    },
    showPreview: {
      control: 'boolean',
      description: 'Show preview text (selector variant only)',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Template for interactive stories
const Template = (args: any) => {
  const { colors } = useThemeStore();
  const [value, setValue] = useState(args.value || args.options[0]?.id);

  return (
    <div className="p-8" style={{ backgroundColor: colors.background }}>
      <ViewModeSelector
        {...args}
        value={value}
        onChange={setValue}
      />
      <div className="mt-4 text-center" style={{ color: colors.text }}>
        Selected: <strong>{value}</strong>
      </div>
    </div>
  );
};

// Default switcher variant
export const Default: Story = {
  render: Template,
  args: {
    options: dataViewModes,
    value: 'list',
    variant: 'switcher',
    size: 'medium',
    showLabels: false,
  },
};

// Toggle variant (like the old ViewToggle)
export const Toggle: Story = {
  render: Template,
  args: {
    options: simpleViewModes,
    value: 'grid',
    variant: 'toggle',
    size: 'medium',
    showLabels: true,
  },
};

// Compact variant
export const Compact: Story = {
  render: Template,
  args: {
    options: discussViewModes,
    value: 'comfortable',
    variant: 'compact',
    size: 'small',
    showLabels: false,
  },
};

// Selector variant (like discuss ViewModeSelector)
export const Selector: Story = {
  render: Template,
  args: {
    options: discussViewModes,
    value: 'cozy',
    variant: 'selector',
    showDescriptions: true,
    showPreview: true,
  },
};

// With labels
export const WithLabels: Story = {
  render: Template,
  args: {
    options: dataViewModes,
    value: 'kanban',
    variant: 'switcher',
    size: 'medium',
    showLabels: true,
  },
};

// Vertical orientation
export const Vertical: Story = {
  render: Template,
  args: {
    options: tableViewModes,
    value: 'table',
    variant: 'toggle',
    orientation: 'vertical',
    showLabels: true,
  },
};

// Large size
export const Large: Story = {
  render: Template,
  args: {
    options: simpleViewModes,
    value: 'grid',
    variant: 'toggle',
    size: 'large',
    showLabels: true,
  },
};

// Small size
export const Small: Story = {
  render: Template,
  args: {
    options: dataViewModes.slice(0, 3),
    value: 'list',
    variant: 'compact',
    size: 'small',
  },
};
