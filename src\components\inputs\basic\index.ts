// Basic Input Components
// Core form input components for text, labels, and basic data entry

// Text Inputs
export { default as Input } from './Input';
export type { InputProps } from './Input';

export { default as PhoneInput } from './PhoneInput';
export type { PhoneInputProps } from './PhoneInput';

// Labels
export { default as Label } from './Label';
export type { LabelProps } from './Label';

// Re-export from forms folder for backward compatibility
export { Input as FormInput } from '../../forms/Input';
export { Label as FormLabel } from '../../forms/Label';
export { TextArea as FormTextArea } from '../../forms/TextArea';
