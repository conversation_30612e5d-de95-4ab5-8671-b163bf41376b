# Global Components

This directory contains truly reusable components that are used across multiple modules and contexts throughout the application. These components are designed to be highly flexible, well-tested, and follow strict design system principles.

## Component Placement Guidelines

### Global Components (src/components/global/)
Components should be placed here if they meet **ALL** of the following criteria:

1. **Cross-Module Usage**: Used in 3+ different modules or contexts
2. **High Reusability**: Generic enough to work in various scenarios
3. **Stable API**: Interface is unlikely to change frequently
4. **Design System Compliance**: Follows design system principles strictly
5. **Well Tested**: Has comprehensive unit tests and Storybook stories
6. **Zero Business Logic**: Contains no domain-specific logic

**Examples of Global Components:**
- ViewModeSelector (used across all data views)
- Dropdown system (used in forms, filters, menus)
- AppNotFound (used for any 404 scenarios)
- Breadcrumb (used in navigation across modules)

### Local Components (src/components/[category]/)
Components should remain in category-specific directories if they:

1. **Category-Specific**: Only used within one component category
2. **Domain-Specific**: Contains business logic or domain knowledge
3. **Experimental**: Still evolving or being tested
4. **Tightly Coupled**: Depends on specific context or data structures

**Examples of Local Components:**
- Form components (specific to form handling)
- Auth components (specific to authentication)
- Chart components (specific to data visualization)

### Module Components (src/modules/[module]/components/)
Components should be in module directories if they:

1. **Module-Specific**: Only used within one module
2. **Feature-Specific**: Implements specific feature functionality
3. **Data-Dependent**: Requires specific data structures or APIs

## Global Component Standards

### 1. API Design
- Props should be generic and flexible
- Use composition over configuration
- Support theming through useThemeStore
- Include comprehensive TypeScript types

### 2. Accessibility
- Full WCAG 2.1 AA compliance
- Proper ARIA attributes
- Keyboard navigation support
- Screen reader compatibility

### 3. Testing
- Unit tests with >90% coverage
- Accessibility tests
- Visual regression tests via Storybook
- Integration tests for complex components

### 4. Documentation
- Comprehensive Storybook stories
- JSDoc comments for all props
- Usage examples and migration guides
- Performance considerations

### 5. Performance
- Optimized for tree-shaking
- Minimal bundle impact
- Efficient re-rendering
- Lazy loading where appropriate

## Current Global Components

### ViewModeSelector
Unified view mode switching component that replaces ViewModeSwitcher and ViewToggle.
- **Usage**: Data views, content displays, layout switching
- **Variants**: toggle, switcher, selector, compact
- **Migration**: Replaces ui/ViewModeSwitcher and ui/ViewToggle

### Dropdown System
Composition-based dropdown system with flexible building blocks.
- **Usage**: Menus, filters, selects, context menus
- **Components**: DropdownBase, DropdownTrigger, DropdownContent, etc.
- **Migration**: Replaces ui/Dropdown and ui/FilterDropdown

### Navigation Components
- **AppNotFound**: 404 error pages
- **Breadcrumb**: Navigation breadcrumbs

## Migration Process

When moving a component to global/:

1. **Audit Usage**: Verify it meets global component criteria
2. **Refactor API**: Make props more generic and flexible
3. **Add Tests**: Ensure comprehensive test coverage
4. **Create Stories**: Add Storybook documentation
5. **Update Imports**: Use codemod or manual updates
6. **Deprecate Old**: Mark original component as deprecated
7. **Document Migration**: Provide clear migration path

## Future Considerations

### Planned Global Components
- Modal/Dialog system
- Toast/Notification system
- Loading/Skeleton components
- Avatar/Profile components
- Badge/Chip components
- Tooltip system

### Component Composition
Global components should support composition patterns:
- Compound components (Modal.Header, Modal.Body, Modal.Footer)
- Render props for flexibility
- Context providers for shared state
- Slot-based composition where appropriate
