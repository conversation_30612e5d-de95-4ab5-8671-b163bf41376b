import { useState } from 'react';
import { useOptimisticMutation } from '../../../hooks/useSWR';
import type { User, CreateUserRequest, UpdateUserRequest } from '../types';
import type { UseUserActionsReturn } from './types';

/**
 * Hook for user CRUD operations with optimistic updates
 */
export function useUserActions(): UseUserActionsReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { mutateOptimistic } = useOptimisticMutation<User[]>();

  const createUser = async (data: CreateUserRequest): Promise<User> => {
    setIsLoading(true);
    setError(null);

    try {
      const newUser: User = {
        id: Date.now().toString(),
        name: data.name,
        email: data.email,
        role: data.role,
        createdAt: new Date().toISOString(),
        status: 'active',
      };

      // Get current users for optimistic update
      const response = await fetch('/api/users');
      const currentUsers = response.ok ? await response.json() : [];

      const optimisticUsers = [...currentUsers, newUser];

      const result = await mutateOptimistic(
        '/users',
        async () => {
          const apiResponse = await fetch('/api/users', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data),
          });

          if (!apiResponse.ok) {
            throw new Error('Failed to create user');
          }

          const createdUser = await apiResponse.json();
          return [...currentUsers, createdUser];
        },
        optimisticUsers,
        { rollbackOnError: true, revalidate: true }
      );

      return newUser;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create user';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const updateUser = async (data: UpdateUserRequest): Promise<User> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/users/${data.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to update user');
      }

      const updatedUser = await response.json();
      return updatedUser;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update user';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const deleteUser = async (id: string): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/users/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete user');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete user';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    createUser,
    updateUser,
    deleteUser,
    isLoading,
    error,
  };
}
