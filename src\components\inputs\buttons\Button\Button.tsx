import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import { LoadingIcon } from '../../icons';

/**
 * @deprecated Use Button from components/forms instead
 * This component will be removed in a future version.
 *
 * Migration:
 * import { Button } from '../../forms';
 *
 * The new Button component provides better form integration,
 * enhanced accessibility, and more consistent styling.
 */

export interface ButtonProps {
  children: React.ReactNode;
  variant?:
    | 'primary'
    | 'secondary'
    | 'outline'
    | 'ghost'
    | 'destructive'
    | 'danger'
    | 'link';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  'data-testid'?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  rounded = 'md',
  shadow = 'sm',
  onClick,
  type = 'button',
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useThemeStore();

  const baseClasses = cn(
    'inline-flex items-center justify-center font-medium transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
    'active:scale-95 transform-gpu',
    fullWidth && 'w-full'
  );

  const sizeClasses = {
    xs: 'h-6 px-2 text-xs',
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 text-base',
    lg: 'h-12 px-6 text-lg',
    xl: 'h-14 px-8 text-xl',
  };

  const roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    full: 'rounded-full',
  };

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm hover:shadow-md',
    md: 'shadow-md hover:shadow-lg',
    lg: 'shadow-lg hover:shadow-xl',
  };

  const variantStyles = {
    primary: {
      backgroundColor: colors.primary,
      color: colors.primaryForeground,
      borderColor: colors.primary,
      '--hover-bg': colors.primary + 'E6', // 90% opacity
      '--focus-ring': colors.primary + '33', // 20% opacity
    },
    secondary: {
      backgroundColor: colors.secondary,
      color: colors.secondaryForeground,
      borderColor: colors.secondary,
      '--hover-bg': colors.secondary + 'E6',
      '--focus-ring': colors.secondary + '33',
    },
    outline: {
      backgroundColor: 'transparent',
      color: colors.primary,
      borderColor: colors.border,
      '--hover-bg': colors.surface,
      '--focus-ring': colors.primary + '33',
    },
    ghost: {
      backgroundColor: 'transparent',
      color: colors.text,
      borderColor: 'transparent',
      '--hover-bg': colors.hover,
      '--focus-ring': colors.primary + '33',
    },
    destructive: {
      backgroundColor: colors.error,
      color: colors.errorForeground,
      borderColor: colors.error,
      '--hover-bg': colors.error + 'E6',
      '--focus-ring': colors.error + '33',
    },
    danger: {
      backgroundColor: colors.error,
      color: colors.errorForeground,
      borderColor: colors.error,
      '--hover-bg': colors.error + 'E6',
      '--focus-ring': colors.error + '33',
    },
    link: {
      backgroundColor: 'transparent',
      color: colors.primary,
      borderColor: 'transparent',
      '--hover-bg': 'transparent',
      '--focus-ring': colors.primary + '33',
    },
  };

  const variantClasses = {
    primary: 'border hover:opacity-90',
    secondary: 'border hover:opacity-90',
    outline: 'border hover:bg-opacity-10',
    ghost: 'border-0 hover:bg-opacity-10',
    destructive: 'border hover:opacity-90',
    danger: 'border hover:opacity-90',
    link: 'border-0 hover:underline shadow-none',
  };

  const buttonClasses = cn(
    baseClasses,
    sizeClasses[size],
    roundedClasses[rounded],
    shadowClasses[shadow],
    variantClasses[variant],
    loading && 'cursor-wait',
    className
  );

  const buttonStyle = {
    ...variantStyles[variant],
  } as React.CSSProperties & {
    '--hover-bg': string;
    '--focus-ring': string;
  };

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled && !loading && onClick) {
      onClick(event);
    }
  };

  return (
    <button
      type={type}
      className={buttonClasses}
      style={buttonStyle}
      disabled={disabled || loading}
      onClick={handleClick}
      data-testid={testId}
      {...props}
    >
      {loading && (
        <LoadingIcon
          className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
          aria-hidden={true}
        />
      )}
      {children}
    </button>
  );
};

export default Button;
