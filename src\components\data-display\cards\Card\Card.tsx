import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';

/**
 * @deprecated Use Card from components/global instead
 * This component will be removed in a future version.
 *
 * Migration:
 * import { Card } from '../../global';
 *
 * The new Card component provides the same API with better
 * theming and performance optimizations.
 */

export interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hoverable?: boolean;
  clickable?: boolean;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  className?: string;
  'data-testid'?: string;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  hoverable = false,
  clickable = false,
  onClick,
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useThemeStore();

  const baseClasses = 'rounded-lg transition-all duration-200';

  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
  };

  const variantStyles = {
    default: {
      backgroundColor: colors.surface,
      border: `1px solid ${colors.border}`,
      boxShadow: 'none',
    },
    elevated: {
      backgroundColor: colors.surface,
      border: 'none',
      boxShadow: `0 1px 3px 0 ${colors.shadow}, 0 1px 2px 0 ${colors.shadow}`,
    },
    outlined: {
      backgroundColor: colors.background,
      border: `2px solid ${colors.border}`,
      boxShadow: 'none',
    },
    filled: {
      backgroundColor: colors.surfaceSecondary,
      border: 'none',
      boxShadow: 'none',
    },
  };

  const hoverStyles =
    hoverable || clickable
      ? {
          '--hover-shadow': `0 4px 6px -1px ${colors.shadow}, 0 2px 4px -1px ${colors.shadow}`,
          '--hover-transform': 'translateY(-1px)',
        }
      : {};

  const cardClasses = `
    ${baseClasses}
    ${paddingClasses[padding]}
    ${hoverable || clickable ? 'hover:shadow-lg hover:transform hover:-translate-y-1' : ''}
    ${clickable ? 'cursor-pointer' : ''}
    ${className}
  `.trim();

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (clickable && onClick) {
      onClick(event);
    }
  };

  return (
    <div
      className={cardClasses}
      style={{
        ...variantStyles[variant],
        ...hoverStyles,
      }}
      onClick={handleClick}
      data-testid={testId}
      {...props}
    >
      {children}
    </div>
  );
};

export default Card;
