import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';
import { useDropdownContext } from './DropdownBase';
import type { DropdownItemProps } from './types';

/**
 * Individual dropdown item component
 */
export const DropdownItem: React.FC<DropdownItemProps> = ({
  children,
  icon,
  shortcut,
  description,
  disabled = false,
  isDivider = false,
  className = '',
  onSelect,
  onClick,
}) => {
  const { colors } = useThemeStore();
  const { onOpenChange, closeOnSelect, size } = useDropdownContext();

  const handleClick = () => {
    if (disabled || isDivider) return;

    onSelect?.();
    onClick?.();

    if (closeOnSelect) {
      onOpenChange(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick();
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          padding: 'px-2 py-1',
          text: 'text-xs',
          icon: 'w-3 h-3',
        };
      case 'large':
        return {
          padding: 'px-4 py-3',
          text: 'text-base',
          icon: 'w-6 h-6',
        };
      default: // medium
        return {
          padding: 'px-3 py-2',
          text: 'text-sm',
          icon: 'w-4 h-4',
        };
    }
  };

  const sizeClasses = getSizeClasses();

  // Render divider
  if (isDivider) {
    return (
      <div
        className={cn('my-1 border-t', className)}
        style={{ borderColor: colors.border }}
        role="separator"
      />
    );
  }

  return (
    <button
      type="button"
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      disabled={disabled}
      className={cn(
        'w-full flex items-center justify-between rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-1',
        sizeClasses.padding,
        sizeClasses.text,
        disabled
          ? 'opacity-50 cursor-not-allowed'
          : 'cursor-pointer hover:bg-opacity-80 focus:ring-blue-500',
        className
      )}
      style={{
        color: disabled ? colors.mutedForeground : colors.text,
        backgroundColor: 'transparent',
      }}
      onMouseEnter={(e) => {
        if (!disabled) {
          e.currentTarget.style.backgroundColor = `${colors.hover}20`;
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled) {
          e.currentTarget.style.backgroundColor = 'transparent';
        }
      }}
      role="menuitem"
      tabIndex={disabled ? -1 : 0}
    >
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        {icon && (
          <div
            className={cn('flex-shrink-0', sizeClasses.icon)}
            style={{ color: colors.mutedForeground }}
          >
            {icon}
          </div>
        )}
        <div className="flex-1 text-left">
          <div className="font-medium truncate">
            {children}
          </div>
          {description && (
            <div
              className={cn('mt-0.5 truncate', size === 'small' ? 'text-xs' : 'text-xs')}
              style={{ color: colors.mutedForeground }}
            >
              {description}
            </div>
          )}
        </div>
      </div>
      
      {shortcut && (
        <div
          className={cn(
            'flex-shrink-0 font-mono px-2 py-1 rounded',
            size === 'small' ? 'text-xs' : 'text-xs'
          )}
          style={{
            backgroundColor: colors.muted,
            color: colors.mutedForeground,
          }}
        >
          {shortcut}
        </div>
      )}
    </button>
  );
};
