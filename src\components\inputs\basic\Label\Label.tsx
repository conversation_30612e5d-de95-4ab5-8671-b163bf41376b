import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface LabelProps
  extends React.LabelHTMLAttributes<HTMLLabelElement> {
  children: React.ReactNode;
  required?: boolean;
  optional?: boolean;
  size?: 'xs' | 'sm' | 'base' | 'lg';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  color?:
    | 'primary'
    | 'secondary'
    | 'muted'
    | 'error'
    | 'warning'
    | 'success'
    | 'info'
    | 'inherit';
  disabled?: boolean;
  'data-testid'?: string;
}

const Label: React.FC<LabelProps> = ({
  children,
  htmlFor,
  required = false,
  optional = false,
  size = 'sm',
  weight = 'medium',
  color = 'inherit',
  disabled = false,
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useThemeStore();

  // Size classes
  const sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
  };

  // Weight classes
  const weightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
  };

  // Color mapping with theme-aware colors
  const getTextColor = () => {
    if (disabled) return colors.disabledForeground;

    switch (color) {
      case 'primary':
        return colors.primary;
      case 'secondary':
        return colors.textSecondary;
      case 'muted':
        return colors.mutedForeground;
      case 'error':
        return colors.error;
      case 'warning':
        return colors.warning;
      case 'success':
        return colors.success;
      case 'info':
        return colors.info;
      case 'inherit':
      default:
        return colors.text;
    }
  };

  // Build classes
  const classes = cn(
    'block select-none',
    sizeClasses[size],
    weightClasses[weight],
    disabled && 'opacity-50 cursor-not-allowed',
    className
  );

  const style = {
    color: getTextColor(),
    ...props.style,
  };

  return (
    <label
      htmlFor={htmlFor}
      className={classes}
      style={style}
      data-testid={testId}
      {...props}
    >
      {children}
      {required && (
        <span
          className="ml-1"
          style={{ color: colors.error }}
          aria-label="required"
        >
          *
        </span>
      )}
      {optional && !required && (
        <span
          className="ml-1 text-xs"
          style={{ color: colors.mutedForeground }}
        >
          (optional)
        </span>
      )}
    </label>
  );
};

export default Label;
