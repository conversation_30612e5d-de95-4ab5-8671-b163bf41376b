import React from 'react';
import { useThemeStore } from '../../../stores/themeStore';
import { cn } from '../../../utils/cn';

export interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hoverable?: boolean;
  clickable?: boolean;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  className?: string;
  'data-testid'?: string;
}

/**
 * Global Card component - moved from ui/Card
 * Provides consistent card styling across the application
 */
export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  hoverable = false,
  clickable = false,
  onClick,
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useThemeStore();

  const baseClasses = 'rounded-lg transition-all duration-200';

  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'elevated':
        return {
          backgroundColor: colors.surface,
          borderColor: 'transparent',
          boxShadow: `0 4px 6px -1px ${colors.shadow}20, 0 2px 4px -1px ${colors.shadow}10`,
        };
      case 'outlined':
        return {
          backgroundColor: colors.surface,
          borderColor: colors.border,
          border: '2px solid',
          boxShadow: 'none',
        };
      case 'filled':
        return {
          backgroundColor: colors.muted,
          borderColor: 'transparent',
          boxShadow: 'none',
        };
      default: // default
        return {
          backgroundColor: colors.surface,
          borderColor: colors.border,
          border: '1px solid',
          boxShadow: 'none',
        };
    }
  };

  const variantStyles = getVariantStyles();

  const hoverStyles = (hoverable || clickable) ? {
    '--hover-shadow': `0 10px 25px -5px ${colors.shadow}25, 0 4px 6px -2px ${colors.shadow}15`,
  } : {};

  const cardClasses = cn(
    baseClasses,
    paddingClasses[padding],
    (hoverable || clickable) && 'hover:shadow-lg hover:transform hover:-translate-y-1',
    clickable && 'cursor-pointer',
    className
  );

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (clickable && onClick) {
      onClick(event);
    }
  };

  return (
    <div
      className={cardClasses}
      style={{
        ...variantStyles,
        ...hoverStyles,
      }}
      onClick={handleClick}
      data-testid={testId}
      {...props}
    >
      {children}
    </div>
  );
};
