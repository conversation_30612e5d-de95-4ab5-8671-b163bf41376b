import React from 'react';
import { cn } from '../../../utils/cn';
import { useDropdownContext } from './DropdownBase';
import type { DropdownContentProps } from './types';

/**
 * Dropdown content wrapper that only renders when dropdown is open
 */
export const DropdownContent: React.FC<DropdownContentProps> = ({
  children,
  className = '',
}) => {
  const { isOpen } = useDropdownContext();

  if (!isOpen) {
    return null;
  }

  return (
    <div className={cn('w-full', className)} role="menu">
      {children}
    </div>
  );
};
