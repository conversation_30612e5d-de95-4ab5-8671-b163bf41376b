import { useUsers as useUsersBase } from '../../../hooks/useSWR';
import type { UseUsersReturn } from './types';

/**
 * Hook for managing users data with SWR
 * Provides users list, loading state, error handling, and data mutation
 */
export function useUsers(): UseUsersReturn {
  const { data: users = [], error, isLoading, mutate } = useUsersBase();

  const refetch = async () => {
    await mutate();
  };

  return {
    users,
    loading: isLoading,
    error: error?.message || null,
    refetch,
    mutate,
  };
}
