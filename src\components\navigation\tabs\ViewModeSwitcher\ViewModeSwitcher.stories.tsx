import type { Meta, StoryObj } from '@storybook/react-vite';
import { ViewModeSwitcher } from './ViewModeSwitcher';
import { useThemeStore } from '../../../stores/themeStore';
import { useEffect, useState } from 'react';

// Mock icons for the stories
const ListIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
  </svg>
);

const GridIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
  </svg>
);

const KanbanIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 0V17m0-10a2 2 0 012-2h2a2 2 0 012 2v10a2 2 0 01-2 2h-2a2 2 0 01-2-2" />
  </svg>
);

const CalendarIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const meta: Meta<typeof ViewModeSwitcher> = {
  title: 'UI/ViewModeSwitcher',
  component: ViewModeSwitcher,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component:
          'A view mode switcher component that allows users to switch between different view modes with enhanced visual feedback.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    viewModes: {
      control: 'object',
      description: 'Array of available view modes with names and icons',
    },
    activeViewMode: {
      control: 'text',
      description: 'Currently active view mode name',
    },
    onViewModeChange: {
      action: 'viewModeChanged',
      description: 'Callback when view mode is changed',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const Template = (args: any) => {
  const { colors, setTheme } = useThemeStore();
  const [activeViewMode, setActiveViewMode] = useState(args.activeViewMode);

  useEffect(() => {
    setTheme('dark');
  }, [setTheme]);

  const handleViewModeChange = (modeName: string) => {
    setActiveViewMode(modeName);
    args.onViewModeChange?.(modeName);
  };

  return (
    <div
      className="p-8"
      style={{ backgroundColor: colors.background }}
    >
      <ViewModeSwitcher
        {...args}
        activeViewMode={activeViewMode}
        onViewModeChange={handleViewModeChange}
      />
      <div className="mt-4 text-center" style={{ color: colors.text }}>
        Active mode: <strong>{activeViewMode}</strong>
      </div>
    </div>
  );
};

export const Default: Story = {
  render: Template,
  args: {
    viewModes: [
      { name: 'list', icon: <ListIcon /> },
      { name: 'grid', icon: <GridIcon /> },
      { name: 'kanban', icon: <KanbanIcon /> },
    ],
    activeViewMode: 'list',
  },
};

export const TwoModes: Story = {
  render: Template,
  args: {
    viewModes: [
      { name: 'list', icon: <ListIcon /> },
      { name: 'grid', icon: <GridIcon /> },
    ],
    activeViewMode: 'grid',
  },
};

export const FourModes: Story = {
  render: Template,
  args: {
    viewModes: [
      { name: 'list', icon: <ListIcon /> },
      { name: 'grid', icon: <GridIcon /> },
      { name: 'kanban', icon: <KanbanIcon /> },
      { name: 'calendar', icon: <CalendarIcon /> },
    ],
    activeViewMode: 'kanban',
  },
};

export const SingleMode: Story = {
  render: Template,
  args: {
    viewModes: [
      { name: 'list', icon: <ListIcon /> },
    ],
    activeViewMode: 'list',
  },
};
